# =============================================================================
# 共行学项目 - 生产环境变量配置文件
# 服务器IP: ************
# 创建时间: 2025-05-30
# =============================================================================

# =============================================================================
# 基础配置
# =============================================================================
SPRING_PROFILES_ACTIVE=prod
SERVER_IP=************
DOMAIN_NAME=************
SERVER_PORT=8081

# =============================================================================
# 数据库配置
# =============================================================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=gongxingxue
DB_USERNAME=gongxingxue
DB_PASSWORD=gxxdb123456
DB_URL=******************************************************************************************************************************************************

# =============================================================================
# 邮件服务配置
# =============================================================================
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=xzkzrwqzuditdhch
MAIL_PROTOCOL=smtp
MAIL_DEFAULT_ENCODING=UTF-8

# =============================================================================
# JWT安全配置
# =============================================================================
JWT_SECRET=gongxingxue_prod_secret_key_for_jwt_token_generation_and_validation_production_environment
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# =============================================================================
# 文件存储配置
# =============================================================================
FILE_UPLOAD_DIR=/app/uploads
FILE_MAX_SIZE=31457280
ALLOWED_FILE_TYPES=pdf,doc,docx,ppt,pptx,xls,xlsx,txt,jpg,jpeg,png,gif

# =============================================================================
# 日志配置
# =============================================================================
LOG_FILE=/app/logs/application.log
LOG_LEVEL=INFO
LOG_DIR=/app/logs

# =============================================================================
# 前端配置
# =============================================================================
FRONTEND_URL=http://************

# =============================================================================
# CORS配置
# =============================================================================
CORS_ALLOWED_ORIGINS=http://************,https://************

# =============================================================================
# Redis配置（如果需要）
# =============================================================================


# =============================================================================
# 应用配置
# =============================================================================
APP_NAME=gongxingxue
APP_VERSION=1.0.0
NODE_ENV=production

# =============================================================================
# 安全配置
# =============================================================================
PASSWORD_SALT=gongxingxue_password_salt_2025
CAPTCHA_EXPIRE_TIME=300

# =============================================================================
# 性能配置
# =============================================================================
DB_POOL_INITIAL_SIZE=5
DB_POOL_MAX_ACTIVE=20
DB_POOL_MAX_IDLE=10
DB_POOL_MIN_IDLE=5
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

# =============================================================================
# 监控配置
# =============================================================================
HEALTH_CHECK_ENABLED=true
MANAGEMENT_ENDPOINTS_ENABLED=true
