import{X as r}from"./index-CWlqViBh.js";function u(e){return r({url:"/resources/public",method:"get",params:e})}function s(e){return r({url:`/resources/${e}`,method:"get"})}function t(){return r({url:"/resources/my",method:"get"})}function c(e){return r({url:`/resources/${e}`,method:"delete"})}function n(e){return r({url:`/resources/download/${e}`,method:"get",responseType:"blob"})}function d(e){return`/api/resources/view/${e}`}export{s as a,d as b,t as c,n as d,c as e,u as g};
