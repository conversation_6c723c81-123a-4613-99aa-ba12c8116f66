package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.Resource;
import com.gongxingxue.entity.ResourceDownload;
import com.gongxingxue.entity.User;
import com.gongxingxue.mapper.ResourceDownloadMapper;
import com.gongxingxue.mapper.ResourceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Resource service
 */
@Service
@RequiredArgsConstructor
public class ResourceService extends ServiceImpl<ResourceMapper, Resource> {

    private final ResourceDownloadMapper resourceDownloadMapper;
    private final FileStorageService fileStorageService;
    private final UserService userService;
    private final MessageService messageService;

    /**
     * Upload resource
     * 缓存策略：上传后清除资源列表缓存，确保新资源能被看到
     */
    @Transactional
    @CacheEvict(value = "resourceList", allEntries = true)
    public Resource uploadResource(String name, String description, Integer examType, MultipartFile file, Long userId) {
        // Store file
        String filePath = fileStorageService.storeFile(file);

        // Create resource
        Resource resource = new Resource()
                .setName(name)
                .setDescription(description)
                .setExamType(examType)
                .setFilePath(filePath)
                .setOriginalFilename(file.getOriginalFilename())
                .setFileSize(file.getSize())
                .setFileType(file.getContentType())
                .setUserId(userId)
                .setDownloadCount(0)
                .setCommentCount(0)
                .setAuditStatus(0) // Pending
                .setCreateTime(LocalDateTime.now());

        // Save resource
        save(resource);

        return resource;
    }

    /**
     * Get resource by ID
     */
    public Resource getResourceById(Long resourceId) {
        return getById(resourceId);
    }

    /**
     * Get resource list with pagination
     * 缓存策略：根据查询参数缓存5分钟，热门查询会被缓存
     */
    @Cacheable(value = "resourceList", key = "'list_' + #examType + '_' + #keyword + '_' + #sortType + '_' + #page + '_' + #size")
    public Page<Resource> getResourceList(Integer examType, String keyword, Integer sortType, Integer page, Integer size) {
        System.out.println("Getting resource list with parameters:");
        System.out.println("examType: " + examType);
        System.out.println("keyword: " + keyword);
        System.out.println("sortType: " + sortType);
        System.out.println("page: " + page);
        System.out.println("size: " + size);

        Page<Resource> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();

        // Only show approved resources
        queryWrapper.eq(Resource::getAuditStatus, 1);

        // Filter by exam type
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }

        // Search by keyword
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper ->
                wrapper.like(Resource::getName, keyword)
                       .or()
                       .like(Resource::getDescription, keyword)
            );
        }

        // Sort by latest or popularity
        if (sortType != null && sortType == 1) {
            // Sort by popularity (total popularity = download_count + comment_count)
            // Use last() to add custom SQL for sorting
            queryWrapper.last("ORDER BY (download_count + comment_count) DESC");
        } else {
            // Sort by latest (audit time)
            queryWrapper.orderByDesc(Resource::getAuditTime);
        }

        Page<Resource> resourcePage = page(pageParam, queryWrapper);

        // Load user information for each resource
        for (Resource resource : resourcePage.getRecords()) {
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }
        }

        return resourcePage;
    }

    /**
     * Get resource list with advanced filters
     */
    public Page<Resource> getResourceListWithAdvancedFilters(Integer examType, String keyword, String sortType,
                                                           String fileType, String timeRange, String downloadRange,
                                                           Integer page, Integer size) {
        // If no advanced filters, use simple query for better performance
        if ((fileType == null || fileType.trim().isEmpty()) &&
            (timeRange == null || timeRange.trim().isEmpty()) &&
            (downloadRange == null || downloadRange.trim().isEmpty())) {

            return getResourceListSimple(examType, keyword, sortType, page, size);
        }

        // Get all approved resources first, then apply filters
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getAuditStatus, 1);

        // Apply basic filters
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }

        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper ->
                wrapper.like(Resource::getName, keyword)
                       .or()
                       .like(Resource::getDescription, keyword)
            );
        }

        // Apply time range filter
        if (timeRange != null && !timeRange.trim().isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = null;

            switch (timeRange) {
                case "today":
                    startTime = now.toLocalDate().atStartOfDay();
                    break;
                case "week":
                    startTime = now.minusWeeks(1);
                    break;
                case "month":
                    startTime = now.minusMonths(1);
                    break;
                case "quarter":
                    startTime = now.minusMonths(3);
                    break;
            }

            if (startTime != null) {
                queryWrapper.ge(Resource::getAuditTime, startTime);
            }
        }

        List<Resource> allResources = list(queryWrapper);

        // Apply advanced filters
        List<Resource> filteredResources = new ArrayList<>();
        for (Resource resource : allResources) {
            // Load user information
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }

            // Apply file type filter
            if (fileType != null && !fileType.trim().isEmpty()) {
                String resourceFileType = getFileTypeCategory(resource.getOriginalFilename());
                if (!fileType.equals(resourceFileType)) {
                    continue;
                }
            }

            // Apply download range filter
            if (downloadRange != null && !downloadRange.trim().isEmpty()) {
                int downloadCount = resource.getDownloadCount();
                boolean matchesRange = false;

                switch (downloadRange) {
                    case "0-10":
                        matchesRange = downloadCount < 10;
                        break;
                    case "10-50":
                        matchesRange = downloadCount >= 10 && downloadCount < 50;
                        break;
                    case "50-100":
                        matchesRange = downloadCount >= 50 && downloadCount < 100;
                        break;
                    case "100+":
                        matchesRange = downloadCount >= 100;
                        break;
                }

                if (!matchesRange) {
                    continue;
                }
            }

            filteredResources.add(resource);
        }

        // Apply sorting
        if (sortType != null) {
            switch (sortType) {
                case "downloadCount":
                    filteredResources.sort((a, b) -> Integer.compare(b.getDownloadCount(), a.getDownloadCount()));
                    break;
                case "favoriteCount":
                    filteredResources.sort((a, b) -> Integer.compare(b.getFavoriteCount(), a.getFavoriteCount()));
                    break;
                case "commentCount":
                    filteredResources.sort((a, b) -> Integer.compare(b.getCommentCount(), a.getCommentCount()));
                    break;
                case "relevance":
                    // For relevance, use a combination of download, comment and favorite count
                    filteredResources.sort((a, b) -> {
                        int scoreA = a.getDownloadCount() + a.getCommentCount() * 2 + a.getFavoriteCount() * 3;
                        int scoreB = b.getDownloadCount() + b.getCommentCount() * 2 + b.getFavoriteCount() * 3;
                        return Integer.compare(scoreB, scoreA);
                    });
                    break;
                case "createTime":
                default:
                    filteredResources.sort((a, b) -> b.getAuditTime().compareTo(a.getAuditTime()));
                    break;
            }
        }

        // Manual pagination
        int total = filteredResources.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Resource> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredResources.subList(start, end);
        }

        // Create page result
        Page<Resource> result = new Page<>(page, size);
        result.setRecords(pageRecords);
        result.setTotal(total);
        result.setPages((total + size - 1) / size);

        return result;
    }

    /**
     * Simple resource list query (for better performance when no advanced filters)
     */
    private Page<Resource> getResourceListSimple(Integer examType, String keyword, String sortType, Integer page, Integer size) {
        Page<Resource> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();

        // Only show approved resources
        queryWrapper.eq(Resource::getAuditStatus, 1);

        // Filter by exam type
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }

        // Search by keyword
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper ->
                wrapper.like(Resource::getName, keyword)
                       .or()
                       .like(Resource::getDescription, keyword)
            );
        }

        // Apply sorting
        if (sortType != null) {
            switch (sortType) {
                case "downloadCount":
                    queryWrapper.orderByDesc(Resource::getDownloadCount);
                    break;
                case "favoriteCount":
                    queryWrapper.orderByDesc(Resource::getFavoriteCount);
                    break;
                case "commentCount":
                    queryWrapper.orderByDesc(Resource::getCommentCount);
                    break;
                case "relevance":
                    // Use custom SQL for relevance scoring (including favorite count)
                    queryWrapper.last("ORDER BY (download_count + comment_count * 2 + favorite_count * 3) DESC");
                    break;
                case "createTime":
                default:
                    queryWrapper.orderByDesc(Resource::getAuditTime);
                    break;
            }
        } else {
            queryWrapper.orderByDesc(Resource::getAuditTime);
        }

        Page<Resource> resourcePage = page(pageParam, queryWrapper);

        // Load user information for each resource
        for (Resource resource : resourcePage.getRecords()) {
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }
        }

        return resourcePage;
    }

    /**
     * Get resources by user ID
     */
    public List<Resource> getResourcesByUserId(Long userId) {
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getUserId, userId);
        queryWrapper.orderByDesc(Resource::getCreateTime);

        return list(queryWrapper);
    }

    /**
     * Get pending resources
     */
    public Page<Resource> getPendingResources(Integer page, Integer size, Integer examType, String fileType, String username, String keyword, LocalDateTime startTime, LocalDateTime endTime) {
        // If no additional filters (fileType, username), use simple query
        if ((fileType == null || fileType.trim().isEmpty() || fileType.equals("null")) &&
            (username == null || username.trim().isEmpty())) {

            Page<Resource> pageParam = new Page<>(page, size);
            LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();

            // Only show pending resources
            queryWrapper.eq(Resource::getAuditStatus, 0);

            // Filter by exam type
            if (examType != null) {
                queryWrapper.eq(Resource::getExamType, examType);
            }

            // Filter by keyword
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like(Resource::getName, keyword.trim());
            }

            // Filter by time range (upload time for pending resources)
            if (startTime != null) {
                queryWrapper.ge(Resource::getCreateTime, startTime);
            }
            if (endTime != null) {
                queryWrapper.le(Resource::getCreateTime, endTime);
            }

            // Sort by creation time
            queryWrapper.orderByAsc(Resource::getCreateTime);

            Page<Resource> resourcePage = page(pageParam, queryWrapper);

            // Load user information for each resource
            for (Resource resource : resourcePage.getRecords()) {
                User user = userService.getUserById(resource.getUserId());
                if (user != null) {
                    resource.setUsername(user.getUsername());
                }
            }

            return resourcePage;
        }

        // If there are additional filters, we need to get all matching records first
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getAuditStatus, 0);

        // Apply basic filters
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(Resource::getName, keyword.trim());
        }
        if (startTime != null) {
            queryWrapper.ge(Resource::getCreateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(Resource::getCreateTime, endTime);
        }
        queryWrapper.orderByAsc(Resource::getCreateTime);

        List<Resource> allResources = list(queryWrapper);

        // Apply additional filters and load user info
        List<Resource> filteredResources = new ArrayList<>();
        for (Resource resource : allResources) {
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }

            // Apply file type filter
            if (fileType != null && !fileType.trim().isEmpty() && !fileType.equals("null")) {
                String resourceFileType = getFileTypeCategory(resource.getOriginalFilename());
                if (!fileType.equals(resourceFileType)) {
                    continue;
                }
            }

            // Apply username filter
            if (username != null && !username.trim().isEmpty()) {
                if (resource.getUsername() == null || !resource.getUsername().toLowerCase().contains(username.toLowerCase())) {
                    continue;
                }
            }

            filteredResources.add(resource);
        }

        // Manual pagination
        int total = filteredResources.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Resource> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredResources.subList(start, end);
        }

        // Create page result
        Page<Resource> result = new Page<>(page, size);
        result.setRecords(pageRecords);
        result.setTotal(total);
        result.setPages((total + size - 1) / size);

        return result;
    }

    /**
     * Get approved resources for admin
     */
    public Page<Resource> getApprovedResources(Integer examType, String fileType, String username, String keyword, Integer page, Integer size, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime auditStartTime, LocalDateTime auditEndTime) {
        // If no additional filters (fileType, username), use simple query
        if ((fileType == null || fileType.trim().isEmpty() || fileType.equals("null")) &&
            (username == null || username.trim().isEmpty())) {

            Page<Resource> pageParam = new Page<>(page, size);
            LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();

            // Only show approved resources
            queryWrapper.eq(Resource::getAuditStatus, 1);

            // Filter by exam type
            if (examType != null) {
                queryWrapper.eq(Resource::getExamType, examType);
            }

            // Filter by keyword
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like(Resource::getName, keyword.trim());
            }

            // Filter by upload time range
            if (startTime != null) {
                queryWrapper.ge(Resource::getCreateTime, startTime);
            }
            if (endTime != null) {
                queryWrapper.le(Resource::getCreateTime, endTime);
            }

            // Filter by audit time range
            if (auditStartTime != null) {
                queryWrapper.ge(Resource::getAuditTime, auditStartTime);
            }
            if (auditEndTime != null) {
                queryWrapper.le(Resource::getAuditTime, auditEndTime);
            }

            // Sort by audit time
            queryWrapper.orderByDesc(Resource::getAuditTime);

            Page<Resource> resourcePage = page(pageParam, queryWrapper);

            // Load user information for each resource
            for (Resource resource : resourcePage.getRecords()) {
                User user = userService.getUserById(resource.getUserId());
                if (user != null) {
                    resource.setUsername(user.getUsername());
                }
            }

            return resourcePage;
        }

        // If there are additional filters, we need to get all matching records first
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getAuditStatus, 1);

        // Apply basic filters
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(Resource::getName, keyword.trim());
        }
        if (startTime != null) {
            queryWrapper.ge(Resource::getCreateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(Resource::getCreateTime, endTime);
        }
        if (auditStartTime != null) {
            queryWrapper.ge(Resource::getAuditTime, auditStartTime);
        }
        if (auditEndTime != null) {
            queryWrapper.le(Resource::getAuditTime, auditEndTime);
        }
        queryWrapper.orderByDesc(Resource::getAuditTime);

        List<Resource> allResources = list(queryWrapper);

        // Apply additional filters and load user info
        List<Resource> filteredResources = new ArrayList<>();
        for (Resource resource : allResources) {
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }

            // Apply file type filter
            if (fileType != null && !fileType.trim().isEmpty() && !fileType.equals("null")) {
                String resourceFileType = getFileTypeCategory(resource.getOriginalFilename());
                if (!fileType.equals(resourceFileType)) {
                    continue;
                }
            }

            // Apply username filter
            if (username != null && !username.trim().isEmpty()) {
                if (resource.getUsername() == null || !resource.getUsername().toLowerCase().contains(username.toLowerCase())) {
                    continue;
                }
            }

            filteredResources.add(resource);
        }

        // Manual pagination
        int total = filteredResources.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Resource> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredResources.subList(start, end);
        }

        // Create page result
        Page<Resource> result = new Page<>(page, size);
        result.setRecords(pageRecords);
        result.setTotal(total);
        result.setPages((total + size - 1) / size);

        return result;
    }

    /**
     * 获取已驳回资料列表（管理员用）
     */
    public Page<Resource> getRejectedResources(Integer examType, String fileType, String username, String keyword, Integer page, Integer size, LocalDateTime startTime, LocalDateTime endTime, LocalDateTime auditStartTime, LocalDateTime auditEndTime) {
        // 如果没有额外的筛选条件（fileType, username），使用简单查询
        if ((fileType == null || fileType.trim().isEmpty() || fileType.equals("null")) &&
            (username == null || username.trim().isEmpty())) {

            Page<Resource> pageParam = new Page<>(page, size);
            LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();

            // 只显示已驳回的资料
            queryWrapper.eq(Resource::getAuditStatus, 2);

            // 按考试类型筛选
            if (examType != null) {
                queryWrapper.eq(Resource::getExamType, examType);
            }

            // 按关键词筛选
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.like(Resource::getName, keyword.trim());
            }

            // 按上传时间筛选
            if (startTime != null) {
                queryWrapper.ge(Resource::getCreateTime, startTime);
            }
            if (endTime != null) {
                queryWrapper.le(Resource::getCreateTime, endTime);
            }

            // 按审核时间筛选
            if (auditStartTime != null) {
                queryWrapper.ge(Resource::getAuditTime, auditStartTime);
            }
            if (auditEndTime != null) {
                queryWrapper.le(Resource::getAuditTime, auditEndTime);
            }

            // 按审核时间倒序排列
            queryWrapper.orderByDesc(Resource::getAuditTime);

            Page<Resource> resourcePage = page(pageParam, queryWrapper);

            // 加载用户信息
            for (Resource resource : resourcePage.getRecords()) {
                User user = userService.getUserById(resource.getUserId());
                if (user != null) {
                    resource.setUsername(user.getUsername());
                }
            }

            return resourcePage;
        }

        // 如果有额外的筛选条件，需要先获取所有匹配的记录
        LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Resource::getAuditStatus, 2);

        // 应用基本筛选条件
        if (examType != null) {
            queryWrapper.eq(Resource::getExamType, examType);
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(Resource::getName, keyword.trim());
        }
        if (startTime != null) {
            queryWrapper.ge(Resource::getCreateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(Resource::getCreateTime, endTime);
        }
        if (auditStartTime != null) {
            queryWrapper.ge(Resource::getAuditTime, auditStartTime);
        }
        if (auditEndTime != null) {
            queryWrapper.le(Resource::getAuditTime, auditEndTime);
        }
        queryWrapper.orderByDesc(Resource::getAuditTime);

        List<Resource> allResources = list(queryWrapper);

        // 应用额外筛选条件并加载用户信息
        List<Resource> filteredResources = new ArrayList<>();
        for (Resource resource : allResources) {
            User user = userService.getUserById(resource.getUserId());
            if (user != null) {
                resource.setUsername(user.getUsername());
            }

            // 应用文件类型筛选
            if (fileType != null && !fileType.trim().isEmpty() && !fileType.equals("null")) {
                String resourceFileType = getFileTypeCategory(resource.getOriginalFilename());
                if (!fileType.equals(resourceFileType)) {
                    continue;
                }
            }

            // 应用用户名筛选
            if (username != null && !username.trim().isEmpty()) {
                if (resource.getUsername() == null || !resource.getUsername().toLowerCase().contains(username.toLowerCase())) {
                    continue;
                }
            }

            filteredResources.add(resource);
        }

        // 手动分页
        int total = filteredResources.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Resource> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredResources.subList(start, end);
        }

        // 创建分页结果
        Page<Resource> result = new Page<>(page, size);
        result.setRecords(pageRecords);
        result.setTotal(total);
        result.setPages((total + size - 1) / size);

        return result;
    }

    /**
     * Get file type category for filtering
     */
    private String getFileTypeCategory(String filename) {
        if (filename == null) {
            return "OTHER";
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toUpperCase();
        }

        switch (extension) {
            case "PDF":
                return "PDF";
            case "DOC":
            case "DOCX":
                return "DOC";
            case "PPT":
            case "PPTX":
                return "PPT";
            case "XLS":
            case "XLSX":
                return "XLS";
            case "TXT":
                return "TXT";
            case "JPG":
            case "JPEG":
            case "PNG":
            case "GIF":
            case "BMP":
                return "IMAGE";
            default:
                return "OTHER";
        }
    }

    /**
     * Approve resource (支持待审核和已驳回资料的通过)
     */
    @Transactional
    public boolean approveResource(Long resourceId, Long adminUserId) {
        Resource resource = getById(resourceId);
        // 允许待审核(0)和已驳回(2)的资料被通过
        if (resource == null || (resource.getAuditStatus() != 0 && resource.getAuditStatus() != 2)) {
            return false;
        }

        resource.setAuditStatus(1); // Approved
        resource.setAuditTime(LocalDateTime.now());
        resource.setAuditUserId(adminUserId);
        // 清除驳回原因
        resource.setRejectReason(null);

        boolean result = updateById(resource);

        // Send approval notification
        if (result) {
            messageService.sendAuditNotification(resource.getUserId(), resource.getName(), 1, null);
        }

        return result;
    }

    /**
     * Reject resource
     */
    @Transactional
    public boolean rejectResource(Long resourceId, String rejectReason, Long adminUserId) {
        Resource resource = getById(resourceId);
        if (resource == null || resource.getAuditStatus() != 0) {
            return false;
        }

        resource.setAuditStatus(2); // Rejected
        resource.setAuditTime(LocalDateTime.now());
        resource.setAuditUserId(adminUserId);
        resource.setRejectReason(rejectReason);

        boolean result = updateById(resource);

        // Send rejection notification
        if (result) {
            messageService.sendAuditNotification(resource.getUserId(), resource.getName(), 2, rejectReason);
        }

        return result;
    }

    /**
     * Batch approve resources
     */
    @Transactional
    public boolean batchApproveResources(List<Long> resourceIds, Long adminUserId) {
        LocalDateTime now = LocalDateTime.now();

        for (Long resourceId : resourceIds) {
            Resource resource = getById(resourceId);
            if (resource != null && resource.getAuditStatus() == 0) {
                resource.setAuditStatus(1); // Approved
                resource.setAuditTime(now);
                resource.setAuditUserId(adminUserId);
                updateById(resource);
            }
        }

        return true;
    }

    /**
     * Batch reject resources
     */
    @Transactional
    public boolean batchRejectResources(List<Long> resourceIds, String rejectReason, Long adminUserId) {
        LocalDateTime now = LocalDateTime.now();

        for (Long resourceId : resourceIds) {
            Resource resource = getById(resourceId);
            if (resource != null && resource.getAuditStatus() == 0) {
                resource.setAuditStatus(2); // Rejected
                resource.setAuditTime(now);
                resource.setAuditUserId(adminUserId);
                resource.setRejectReason(rejectReason);
                updateById(resource);
            }
        }

        return true;
    }

    /**
     * Download resource
     */
    @Transactional
    public void downloadResource(Long resourceId, Long userId, String ip, String userAgent) {
        // Increment download count
        Resource resource = getById(resourceId);
        if (resource != null) {
            resource.setDownloadCount(resource.getDownloadCount() + 1);
            updateById(resource);

            // Record download
            ResourceDownload download = new ResourceDownload()
                    .setResourceId(resourceId)
                    .setUserId(userId)
                    .setIp(ip)
                    .setUserAgent(userAgent)
                    .setCreateTime(LocalDateTime.now());

            resourceDownloadMapper.insert(download);
        }
    }

    /**
     * Delete resource
     */
    @Transactional
    public boolean deleteResource(Long resourceId, Long userId) {
        Resource resource = getById(resourceId);
        if (resource == null || !resource.getUserId().equals(userId)) {
            return false;
        }

        // Delete file
        fileStorageService.deleteFile(resource.getFilePath());

        // Delete resource
        return removeById(resourceId);
    }

    /**
     * Update comment count
     */
    public void updateCommentCount(Long resourceId, int delta) {
        Resource resource = getById(resourceId);
        if (resource != null) {
            resource.setCommentCount(resource.getCommentCount() + delta);
            updateById(resource);
        }
    }
}
