import{u as se,c as N,E as k,D as ue,k as me,r as y,o as de,g as C,b as g,l as r,d as o,i as B,e as l,f as m,m as b,t as d,j as te,G as ae,h as H,F as ee,v as ge,p as he,q as ce,s as _e,H as ye,I as Z}from"./index-CWlqViBh.js";import{a as we,d as Ce,b as ke}from"./resource-D7yiaq06.js";import{g as pe,a as ie,b as be,d as xe}from"./comment-cGtgcCPc.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";function Te(){const p=se(),A=me(),x=N(()=>p.isLoggedIn),T=N(()=>p.user),R=N(()=>{var u;return((u=p.user)==null?void 0:u.role)===1}),n=async(u="此操作",w=!0)=>{try{await ue.confirm(`${u}需要登录，是否前往登录页面？`,"需要登录",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"info",center:!0}),A.push({path:"/login",query:{redirect:A.currentRoute.value.fullPath}})}catch{w||k.info("操作已取消")}};return{isLoggedIn:x,currentUser:T,isAdmin:R,showLoginPrompt:n,checkAuthAndExecute:async(u,w={})=>{const{requireAuth:L=!0,requireAdmin:$=!1,actionName:D="此操作",showPrompt:F=!0}=w;if(!L)return await u();if(!x.value)return F?await n(D):k.warning(`${D}需要登录`),!1;if($&&!R.value)return k.error("权限不足，需要管理员权限"),!1;try{return await u()}catch(I){return console.error("操作执行失败:",I),k.error("操作失败，请稍后重试"),!1}},hasPermission:u=>{switch(u){case"login":return x.value;case"admin":return R.value;default:return!0}},getActionConfig:u=>({download:{requireAuth:!0,actionName:"下载资料",icon:"Download",text:x.value?"下载":"登录后下载"},favorite:{requireAuth:!0,actionName:"收藏资料",icon:"Star",text:x.value?"收藏":"登录后收藏"},comment:{requireAuth:!0,actionName:"发表评论",icon:"ChatDotRound",text:x.value?"发表评论":"登录后评论"},upload:{requireAuth:!0,actionName:"上传资料",icon:"Upload",text:x.value?"上传资料":"登录后上传"},admin:{requireAuth:!0,requireAdmin:!0,actionName:"管理操作",icon:"Setting",text:"管理"}})[u]||{requireAuth:!1,actionName:"操作",text:"操作"}}}const Re=["data-comment-id"],$e={class:"comment-header"},Ie={class:"user-info"},Ae={class:"user-details"},De={class:"username"},qe={class:"comment-time"},Se={key:0,class:"comment-actions"},Ee={class:"comment-content"},Le={key:0,class:"comment-replies"},Ne=["data-comment-id"],Ue={class:"reply-header"},Be={class:"user-info"},Pe={class:"user-details"},ze={class:"username"},Fe={class:"comment-time"},Ve={key:0,class:"comment-actions"},Me={class:"reply-content"},je={key:1,class:"load-replies"},Oe={__name:"CommentItem",props:{comment:{type:Object,required:!0},showActions:{type:Boolean,default:!0}},emits:["reply","delete","refresh-replies"],setup(p,{expose:A,emit:x}){const T=p,R=x,n=se(),S=y([]),E=y(!1),U=y(!1),u=N(()=>{var _,P;const c=((_=T.comment.user)==null?void 0:_.nickname)||((P=T.comment.user)==null?void 0:P.username)||"";return c?c.charAt(0).toUpperCase():"U"}),w=N(()=>n.isLoggedIn&&(n.user.id===T.comment.userId||n.isAdmin)),L=N(()=>!U.value&&T.comment.replyCount>0),$=c=>c?c.charAt(0).toUpperCase():"U",D=c=>n.isLoggedIn&&(n.user.id===c.userId||n.isAdmin),F=()=>{R("reply",T.comment)},I=c=>{R("reply",c)},v=()=>{R("delete",T.comment.id)},oe=c=>{R("delete",c.id)},j=async()=>{if(!E.value){E.value=!0;try{const c=await pe(T.comment.id);S.value=c.data,U.value=!0}catch(c){console.error("Failed to load replies:",c)}finally{E.value=!1}}};return A({refreshReplies:async()=>{U.value=!1,await j()}}),de(()=>{j()}),(c,_)=>{var G,K,Q;const P=C("el-avatar"),z=C("el-button");return r(),g("div",{class:"comment-item","data-comment-id":p.comment.id},[o("div",$e,[o("div",Ie,[l(P,{size:40,src:(G=p.comment.user)==null?void 0:G.avatar},{default:m(()=>[b(d(u.value),1)]),_:1},8,["src"]),o("div",Ae,[o("div",De,d(((K=p.comment.user)==null?void 0:K.nickname)||((Q=p.comment.user)==null?void 0:Q.username)||"匿名用户"),1),o("div",qe,d(te(ae)(p.comment.createTime)),1)])]),p.showActions?(r(),g("div",Se,[l(z,{type:"text",size:"small",onClick:F},{default:m(()=>_[0]||(_[0]=[b("回复")])),_:1,__:[0]}),w.value?(r(),H(z,{key:0,type:"text",size:"small",onClick:v},{default:m(()=>_[1]||(_[1]=[b("删除")])),_:1,__:[1]})):B("",!0)])):B("",!0)]),o("div",Ee,[o("p",null,d(p.comment.content),1)]),S.value.length>0?(r(),g("div",Le,[(r(!0),g(ee,null,ge(S.value,f=>{var O,W,J;return r(),g("div",{class:"reply-item",key:f.id,"data-comment-id":f.id},[o("div",Ue,[o("div",Be,[l(P,{size:30,src:(O=f.user)==null?void 0:O.avatar},{default:m(()=>{var e,t;return[b(d($(((e=f.user)==null?void 0:e.nickname)||((t=f.user)==null?void 0:t.username))),1)]}),_:2},1032,["src"]),o("div",Pe,[o("div",ze,d(((W=f.user)==null?void 0:W.nickname)||((J=f.user)==null?void 0:J.username)||"匿名用户"),1),o("div",Fe,d(te(ae)(f.createTime)),1)])]),p.showActions?(r(),g("div",Ve,[l(z,{type:"text",size:"small",onClick:e=>I(f)},{default:m(()=>_[2]||(_[2]=[b("回复")])),_:2,__:[2]},1032,["onClick"]),D(f)?(r(),H(z,{key:0,type:"text",size:"small",onClick:e=>oe(f)},{default:m(()=>_[3]||(_[3]=[b("删除")])),_:2,__:[3]},1032,["onClick"])):B("",!0)])):B("",!0)]),o("div",Me,[o("p",null,d(f.content),1)])],8,Ne)}),128))])):B("",!0),L.value?(r(),g("div",je,[l(z,{type:"text",onClick:j},{default:m(()=>[b(d(E.value?"加载中...":"查看更多回复"),1)]),_:1})])):B("",!0)],8,Re)}}},He=ve(Oe,[["__scopeId","data-v-54cf9188"]]),Ge={class:"resource-detail"},Ke={class:"resource-header"},Qe={class:"resource-title"},We={class:"resource-meta"},Je={class:"meta-item"},Xe={class:"meta-item"},Ye={class:"meta-item"},Ze={class:"resource-actions"},et={class:"resource-description card"},tt={class:"resource-comments card"},ot={key:0,class:"comment-form"},nt={class:"comment-form-actions"},at={key:1,class:"login-to-comment"},st={class:"comments-list"},lt={key:0,class:"pagination"},rt={__name:"ResourceDetail",setup(p){const A=he(),x=me();se();const{isLoggedIn:T,checkAuthAndExecute:R}=Te(),n=y(null),S=y(!1),E=y([]),U=y(!1),u=y(0),w=y(1),L=y(10),$=y(""),D=y(null),F=y(0),I=N(()=>Number(A.params.id)),v=N(()=>A.query.commentId?Number(A.query.commentId):null),oe=N(()=>n.value?["考研","考公","法考","教资","其他"][n.value.examType]||"未知":""),j=async()=>{S.value=!0;try{const e=await we(I.value);n.value=e.data}catch(e){console.error("Failed to fetch resource details:",e),k.error("获取资料详情失败")}finally{S.value=!1}},V=async()=>{U.value=!0;try{const e=await ie(I.value,w.value,L.value);E.value=e.data.records,u.value=e.data.total,F.value++,v.value?(console.log("Found target comment ID in URL:",v.value),await Z(),J()):console.log("No target comment ID in URL, route.query:",A.query)}catch(e){console.error("Failed to fetch comments:",e)}finally{U.value=!1}},c=async()=>await R(async()=>{try{const e=await Ce(I.value);if(!e||!e.data)throw new Error("下载响应为空");const t=e.data;if(!(t instanceof Blob)||t.size===0)throw new Error("下载的文件为空或格式错误");console.log("Downloaded blob size:",t.size,"bytes"),console.log("Downloaded blob type:",t.type);let s=n.value.originalFilename||n.value.name||`resource_${I.value}`;const a=window.URL.createObjectURL(t),i=document.createElement("a");i.href=a,i.download=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(a),console.log("Download initiated for:",s,"Size:",t.size,"bytes")}catch(e){console.error("Download failed:",e),k.error("下载失败："+(e.message||"请稍后重试"))}},{actionName:"下载资料"}),_=()=>{var a;const t=(a=(n.value.originalFilename||n.value.name).split(".").pop())==null?void 0:a.toLowerCase();if(["pdf","txt","jpg","jpeg","png","gif","mp4","mp3"].includes(t)){const i=ke(I.value);window.open(i,"_blank")}else k.warning({message:`${t.toUpperCase()} 文件无法在线预览，将在 2 秒后自动下载`,duration:2e3,showClose:!0}),setTimeout(()=>{c()},2e3)},P=async()=>{if($.value.trim())return await R(async()=>{try{const e={resourceId:I.value,content:$.value};D.value&&(e.parentId=D.value.id),await be(e),k.success(D.value?"回复成功":"评论成功"),await new Promise(t=>setTimeout(t,200)),await V(),$.value="",D.value=null,n.value&&(n.value.commentCount+=1)}catch(e){console.error("Failed to submit comment:",e),k.error("评论失败，请稍后重试")}},{actionName:"发表评论"})},z=e=>{var a,i;D.value=e;const t=((a=e.user)==null?void 0:a.nickname)||((i=e.user)==null?void 0:i.username)||`用户${e.userId}`;$.value=`@${t} `;const s=document.querySelector(".comment-form");s&&s.scrollIntoView({behavior:"smooth"})},G=async e=>{try{await ue.confirm("确定要删除这条评论吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await xe(e),k.success("评论已删除"),V(),n.value&&(n.value.commentCount-=1)}catch(t){t!=="cancel"&&(console.error("Failed to delete comment:",t),k.error("删除评论失败"))}},K=e=>{w.value=e,V()},Q=async()=>{if(!v.value)return null;console.log("Searching for comment",v.value,"across all pages");const e=Math.ceil(u.value/L.value);console.log("Total pages to search:",e);for(let t=1;t<=e;t++)try{console.log("Searching page",t);const a=(await ie(I.value,t,L.value)).data.records;if(a.find(h=>String(h.id)===String(v.value)))return console.log("Found target comment (top-level) on page",t),{page:t,parentId:null};for(const h of a)if(h.replyCount>0)try{if((await pe(h.id)).data.find(ne=>String(ne.id)===String(v.value)))return console.log("Found target comment (reply) on page",t,"under parent",h.id),{page:t,parentId:h.id}}catch(q){console.error("Error loading replies for comment",h.id,q)}}catch(s){console.error("Error searching page",t,s)}return console.log("Target comment not found in any page"),null},f=async e=>{console.log("Ensuring replies for parent",e,"are loaded...");const t=document.querySelector(`[data-comment-id="${e}"]`);if(t){const s=t.querySelector(".load-replies .el-button");s&&s.textContent.includes("查看更多回复")?(console.log("Clicking load more replies button for parent",e),s.click(),await new Promise(a=>setTimeout(a,1e3))):console.log("No load more button found for parent",e,"replies might already be loaded")}else console.log("Parent element not found for ID",e)},O=async()=>{console.log("Ensuring all replies are loaded...");const e=document.querySelectorAll(".load-replies .el-button");console.log("Found",e.length,"load more buttons");for(const t of e)t.textContent.includes("查看更多回复")&&(console.log("Clicking load more replies button"),t.click(),await new Promise(s=>setTimeout(s,500)))},W=async(e=15)=>{for(let t=0;t<e;t++){const s=document.querySelector(`[data-comment-id="${v.value}"]`);if(s)return console.log("Target element found after",t+1,"attempts"),s;if(t===0||t===e-1){console.log("Waiting for target element, attempt",t+1),console.log("Looking for comment ID:",v.value);const a=document.querySelectorAll("[data-comment-id]");console.log("All comment elements found:",a.length),a.forEach((i,h)=>{const q=i.getAttribute("data-comment-id"),M=i.classList.contains("reply-item");console.log(`  ${h+1}. Comment ID: ${q} (${M?"reply":"top-level"})`)})}await new Promise(a=>setTimeout(a,300))}return null},J=async()=>{if(console.log("scrollToTargetComment called, targetCommentId:",v.value),!v.value){console.log("No target comment ID found");return}let e=document.querySelector(`[data-comment-id="${v.value}"]`);if(console.log("Target element found on current page:",e),e||(console.log("Target not found, trying to load all replies first..."),await O(),await Z(),e=document.querySelector(`[data-comment-id="${v.value}"]`),console.log("Target element found after loading replies:",e)),!e){console.log("Target not found on current page, searching other pages...");const t=await Q();if(t){const{page:s,parentId:a}=t;s!==w.value&&(console.log("Switching to page",s),w.value=s,await V(),await Z()),a?(console.log("Target is a reply under parent",a,"ensuring parent replies are loaded"),await f(a)):await O(),await Z(),console.log("After page switch and reply loading, checking DOM...");const i=document.querySelectorAll("[data-comment-id]");console.log("All comment elements found after page switch:",i.length),i.forEach((h,q)=>{const M=h.getAttribute("data-comment-id"),X=h.classList.contains("reply-item");console.log(`  ${q+1}. Comment ID: ${M} (${X?"reply":"top-level"})`)}),e=await W()}}if(e){console.log("Scrolling to target comment and adding highlight"),e.scrollIntoView({behavior:"smooth",block:"center"}),e.classList.add("highlight-comment"),setTimeout(()=>{e.classList.remove("highlight-comment"),console.log("Highlight removed")},3e3);const t={...A.query};delete t.commentId,x.replace({query:t})}else console.log("Target element still not found after searching all pages"),k.warning("未找到指定的评论，可能已被删除")};return de(()=>{j(),V()}),(e,t)=>{const s=C("el-icon-download"),a=C("el-icon"),i=C("el-icon-chat-dot-round"),h=C("el-icon-clock"),q=C("el-button"),M=C("el-icon-view"),X=C("el-input"),ne=C("router-link"),fe=C("el-pagination"),le=C("el-empty"),re=_e("loading");return ce((r(),g("div",Ge,[n.value?(r(),g(ee,{key:0},[o("div",Ke,[o("div",Qe,[o("span",{class:ye(["exam-type-tag",`exam-type-${n.value.examType}`])},d(oe.value),3),o("h1",null,d(n.value.name),1)]),o("div",We,[o("div",Je,[l(a,null,{default:m(()=>[l(s)]),_:1}),o("span",null,d(n.value.downloadCount)+" 下载",1)]),o("div",Xe,[l(a,null,{default:m(()=>[l(i)]),_:1}),o("span",null,d(n.value.commentCount)+" 评论",1)]),o("div",Ye,[l(a,null,{default:m(()=>[l(h)]),_:1}),o("span",null,d(te(ae)(n.value.auditTime)),1)])])]),o("div",Ze,[l(q,{type:"primary",onClick:c},{default:m(()=>[l(a,null,{default:m(()=>[l(s)]),_:1}),t[1]||(t[1]=b(" 下载资料 "))]),_:1,__:[1]}),l(q,{type:"success",onClick:_},{default:m(()=>[l(a,null,{default:m(()=>[l(M)]),_:1}),t[2]||(t[2]=b(" 在线查看 "))]),_:1,__:[2]})]),o("div",et,[t[3]||(t[3]=o("h3",null,"资料简介",-1)),o("p",null,d(n.value.description||"暂无简介"),1)]),o("div",tt,[o("h3",null,"评论区 ("+d(n.value.commentCount)+")",1),te(T)?(r(),g("div",ot,[l(X,{modelValue:$.value,"onUpdate:modelValue":t[0]||(t[0]=Y=>$.value=Y),type:"textarea",rows:3,placeholder:"发表您的评论...",maxlength:"500","show-word-limit":""},null,8,["modelValue"]),o("div",nt,[l(q,{type:"primary",disabled:!$.value.trim(),onClick:P},{default:m(()=>t[4]||(t[4]=[b(" 发表评论 ")])),_:1,__:[4]},8,["disabled"])])])):(r(),g("div",at,[l(ne,{to:"/login"},{default:m(()=>t[5]||(t[5]=[b("登录")])),_:1,__:[5]}),t[6]||(t[6]=b(" 后参与评论 "))])),ce((r(),g("div",st,[E.value.length>0?(r(),g(ee,{key:0},[(r(!0),g(ee,null,ge(E.value,Y=>(r(),H(He,{key:`${Y.id}-${F.value}`,comment:Y,onReply:z,onDelete:G},null,8,["comment"]))),128)),u.value>L.value?(r(),g("div",lt,[l(fe,{background:"",layout:"prev, pager, next",total:u.value,"page-size":L.value,"current-page":w.value,onCurrentChange:K},null,8,["total","page-size","current-page"])])):B("",!0)],64)):(r(),H(le,{key:1,description:"暂无评论"}))])),[[re,U.value]])])],64)):S.value?B("",!0):(r(),H(le,{key:1,description:"资料不存在或已被删除"}))])),[[re,S.value]])}}},dt=ve(rt,[["__scopeId","data-v-11cfa142"]]);export{dt as default};
