import{r as p,o as x,a as y,b as N,d as e,e as t,f as o,g as w,m as c,H as L,t as k,E as l,k as V,l as u,j as d,U as b,h as g,i as z,T as B,P as T,V as S}from"./index-CWlqViBh.js";import{_ as D}from"./_plugin-vue_export-helper-DlAUqK2U.js";const H={class:"network-error"},M={class:"error-container"},O={class:"error-illustration"},U={class:"error-icon"},j={class:"error-content"},A={class:"action-buttons"},G={class:"network-status"},I={class:"status-text"},P={__name:"NetworkError",setup(R){const h=V(),n=p(!1),a=p({text:"检测网络状态中...",icon:"Connection",class:"status-checking"}),i=()=>{navigator.onLine?a.value={text:"网络连接正常",icon:"CircleCheck",class:"status-online"}:a.value={text:"网络连接断开",icon:"CircleClose",class:"status-offline"}},C=async()=>{n.value=!0;try{if((await fetch("/api/health",{method:"GET",timeout:5e3})).ok)l.success("连接恢复，正在跳转..."),setTimeout(()=>{window.location.reload()},1e3);else throw new Error("服务器响应异常")}catch{l.error("连接仍然异常，请稍后再试")}finally{n.value=!1}},E=()=>{h.push("/")},_=()=>{i(),l.success("网络连接已恢复")},f=()=>{i(),l.warning("网络连接已断开")};return x(()=>{i(),window.addEventListener("online",_),window.addEventListener("offline",f)}),y(()=>{window.removeEventListener("online",_),window.removeEventListener("offline",f)}),(v,s)=>{const r=w("el-icon"),m=w("el-button");return u(),N("div",H,[e("div",M,[e("div",O,[e("div",U,[t(r,{size:120,color:"#E6A23C"},{default:o(()=>[t(d(b))]),_:1})])]),e("div",j,[s[1]||(s[1]=e("h1",{class:"error-title"},"网络连接异常",-1)),s[2]||(s[2]=e("p",{class:"error-description"}," 无法连接到服务器，请检查您的网络连接。 ",-1)),s[3]||(s[3]=e("p",{class:"error-suggestion"}," 您可以： ",-1)),s[4]||(s[4]=e("ul",{class:"suggestion-list"},[e("li",null,"检查网络连接是否正常"),e("li",null,"尝试刷新页面"),e("li",null,"检查防火墙设置"),e("li",null,"稍后再试")],-1)),e("div",A,[t(m,{type:"primary",size:"large",onClick:C,loading:n.value},{default:o(()=>[n.value?z("",!0):(u(),g(r,{key:0},{default:o(()=>[t(d(B))]),_:1})),c(" "+k(n.value?"重试中...":"重试连接"),1)]),_:1},8,["loading"]),t(m,{size:"large",onClick:E},{default:o(()=>[t(r,null,{default:o(()=>[t(d(T))]),_:1}),s[0]||(s[0]=c(" 返回首页 "))]),_:1,__:[0]})]),e("div",G,[e("p",I,[t(r,{class:L(a.value.class)},{default:o(()=>[(u(),g(S(a.value.icon)))]),_:1},8,["class"]),c(" "+k(a.value.text),1)])])])])])}}},J=D(P,[["__scopeId","data-v-764fa02c"]]);export{J as default};
