package com.gongxingxue.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gongxingxue.common.Result;
import com.gongxingxue.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 认证拦截器
 *
 * 这个拦截器负责验证用户的JWT token，确保只有已认证的用户才能访问受保护的API。
 * 工作流程：
 * 1. 检查请求路径是否在白名单中（如登录、注册等公开接口）
 * 2. 从请求头中提取JWT token
 * 3. 验证token的有效性
 * 4. 解析token中的用户信息并设置到请求属性中
 * 5. 允许请求继续执行或返回401错误
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private final JwtUtil jwtUtil;
    private final ObjectMapper objectMapper;

    // 🎯 简化的权限白名单
    // 明确定义哪些功能可以匿名访问：
    // 1. 浏览和查看功能（不包括下载）
    // 2. 认证相关功能
    // 3. 系统功能（API文档等）
    private final List<String> excludePaths = Arrays.asList(
            // 认证相关（必须公开）
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/captcha",
            "/api/auth/forgot-password",
            "/api/auth/verify-reset-token",
            "/api/auth/reset-password",

            // 浏览功能（公开访问，提升用户体验）
            "/api/resources/public",        // 资料列表
            "/api/resources/view",          // 文件预览
            "/api/comments/resource",       // 查看评论

            // 系统功能
            "/api/admin/resources/preview", // 管理员预览
            "/api/admin/resources/download", // 管理员下载
            "/swagger-ui",
            "/swagger-resources",
            "/v3/api-docs"
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestPath = request.getRequestURI();
        System.out.println("AuthInterceptor: Processing request: " + requestPath);

        // Skip non-controller methods
        if (!(handler instanceof HandlerMethod)) {
            System.out.println("AuthInterceptor: Skipping non-controller method");
            return true;
        }

        // Check if path is excluded from authentication
        for (String path : excludePaths) {
            if (requestPath.startsWith(path)) {
                System.out.println("AuthInterceptor: Path excluded from authentication: " + requestPath + " (matched: " + path + ")");
                return true;
            }
        }

        // 🎯 特殊处理：资料详情页面允许匿名访问（仅查看，不包括下载等操作）
        if (requestPath.matches("/api/resources/\\d+") && "GET".equals(request.getMethod())) {
            System.out.println("AuthInterceptor: Resource detail view allowed for anonymous access: " + requestPath);
            return true;
        }

        System.out.println("AuthInterceptor: Path requires authentication: " + requestPath);

        // 第二步：从请求头中获取JWT token
        // 标准的JWT传递方式是在Authorization头中，格式为 "Bearer <token>"
        String token = request.getHeader("Authorization");
        if (!StringUtils.hasText(token) || !token.startsWith("Bearer ")) {
            log.warn("认证失败：请求路径 {} 缺少有效的Authorization头", requestPath);
            sendErrorResponse(response, 401, "未授权：缺少访问令牌");
            return false;
        }

        // 第三步：提取token值（去掉"Bearer "前缀）
        token = token.substring(7);

        // 第四步：验证token的有效性
        // 包括格式验证、签名验证、过期时间验证等
        if (!jwtUtil.validateToken(token)) {
            log.warn("认证失败：请求路径 {} 的token无效或已过期", requestPath);
            sendErrorResponse(response, 401, "未授权：访问令牌无效或已过期");
            return false;
        }

        // 第五步：从token中解析用户信息
        // 这些信息将被设置到请求属性中，供后续的Controller使用
        String username = jwtUtil.getUsernameFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        Integer userRole = jwtUtil.getRoleFromToken(token);

        // 第六步：将用户信息设置到请求属性中
        // 这样Controller就可以通过request.getAttribute()获取当前用户信息
        request.setAttribute("username", username);
        request.setAttribute("userId", userId);
        request.setAttribute("userRole", userRole);

        log.debug("认证成功：用户 {} (ID: {}, 角色: {}) 访问路径 {}",
            username, userId, userRole, requestPath);

        return true;
    }

    /**
     * Send error response
     */
    private void sendErrorResponse(HttpServletResponse response, int status, String message) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(Result.error(status, message)));
    }
}
