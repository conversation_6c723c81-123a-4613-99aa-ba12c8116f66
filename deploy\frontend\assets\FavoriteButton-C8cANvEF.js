import{X as F,a0 as k,x as S,u as b,r as p,w as y,a as x,g as B,h as E,l as C,f as M,m as N,b as T,i as z,t as I,j as w,a1 as L,a2 as U,E as v}from"./index-CWlqViBh.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";function $(r){return F({url:`/favorites/${r}/toggle`,method:"put"})}function j(r){return F({url:`/favorites/check/${r}`,method:"get"})}function q(r={}){return F({url:"/favorites/my",method:"get",params:r})}const a=S({favorites:[],favoriteIds:new Set,loading:!1,total:0}),m=new Set;function D(){const r=async(e=1,i=12)=>{a.loading=!0;try{console.log("Fetching favorites from server...",{page:e,size:i});const o=await q({page:e,size:i});if(o.success)return a.favorites=o.data.records,a.total=o.data.total,a.favoriteIds.clear(),o.data.records.forEach(u=>{a.favoriteIds.add(u.id)}),o.data;console.error("Fetch favorites failed:",o)}catch(o){console.error("Fetch favorites error:",o)}finally{a.loading=!1}},g=e=>a.favoriteIds.has(e),s=e=>{a.favoriteIds.has(e.id)||(a.favorites.unshift(e),a.favoriteIds.add(e.id),a.total++,f(e.id,!0))},n=e=>{a.favoriteIds.has(e)&&(a.favorites=a.favorites.filter(i=>i.id!==e),a.favoriteIds.delete(e),a.total=Math.max(0,a.total-1),f(e,!1))},c=(e,i,o=null)=>{i&&o?s(o):n(e)},f=(e,i)=>{m.forEach(o=>{try{o(e,i)}catch(u){console.error("Favorite change callback error:",u)}})},h=e=>(m.add(e),()=>{m.delete(e)}),l=()=>r();return{...k(a),fetchFavorites:r,isFavorited:g,addToFavorites:s,removeFromFavorites:n,toggleFavoriteState:c,onFavoriteChange:h,refreshFavorites:l}}const R={key:0,class:"count"},X={__name:"FavoriteButton",props:{resourceId:{type:Number,required:!0},initialFavorited:{type:Boolean,default:!1},favoriteCount:{type:Number,default:0},showCount:{type:Boolean,default:!0},size:{type:String,default:"default"}},emits:["update:favorited","update:count"],setup(r,{emit:g}){const s=r,n=g,c=b(),{toggleFavoriteState:f,onFavoriteChange:h}=D(),l=p(!1),e=p(s.initialFavorited);y(()=>s.initialFavorited,t=>{e.value=t});const i=async()=>{if(!c.isLoggedIn){v.warning("请先登录后再收藏");return}l.value=!0;try{const t=await $(s.resourceId);if(t.success){e.value=t.data,v.success(t.message),f(s.resourceId,t.data,{id:s.resourceId,favoriteCount:s.favoriteCount}),n("update:favorited",t.data);const d=t.data?s.favoriteCount+1:Math.max(0,s.favoriteCount-1);n("update:count",d)}else v.error(t.message||"操作失败")}catch(t){console.error("Toggle favorite error:",t),v.error("操作失败，请稍后重试")}finally{l.value=!1}},o=async()=>{if(c.isLoggedIn)try{const t=await j(s.resourceId);t.success&&(e.value=t.data,n("update:favorited",t.data))}catch(t){console.error("Check favorite status error:",t)}},u=h((t,d)=>{t===s.resourceId&&(e.value=d,n("update:favorited",d))});return y(()=>c.isLoggedIn,t=>{t?o():e.value=!1},{immediate:!0}),x(()=>{u()}),(t,d)=>{const _=B("el-button");return C(),E(_,{type:e.value?"warning":"default",icon:e.value?w(L):w(U),loading:l.value,size:r.size,onClick:i,class:"favorite-button"},{default:M(()=>[N(I(e.value?"已收藏":"收藏")+" ",1),r.showCount&&r.favoriteCount>0?(C(),T("span",R," ("+I(r.favoriteCount)+") ",1)):z("",!0)]),_:1},8,["type","icon","loading","size"])}}},H=V(X,[["__scopeId","data-v-3f55f8a0"]]);export{H as F,D as u};
