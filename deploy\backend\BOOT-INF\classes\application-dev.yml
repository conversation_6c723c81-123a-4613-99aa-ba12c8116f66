# 开发环境配置
spring:
  # 数据库配置 - 开发环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      # 开发环境连接池配置（较小）
      minimum-idle: ${DB_MIN_IDLE:5}
      maximum-pool-size: ${DB_MAX_POOL_SIZE:15}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
      pool-name: GongxingxueHikariPool-Dev

  # 邮件配置 - 开发环境
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your_auth_code}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

# 文件存储配置 - 开发环境
file:
  upload-dir: ${FILE_UPLOAD_DIR:./uploads/dev}

# JWT配置 - 开发环境
jwt:
  secret: ${JWT_SECRET:gongxingxue_dev_secret_key_for_jwt_token_generation}
  expiration: ${JWT_EXPIRATION:86400000}  # 24小时

# Swagger配置 - 开发环境启用
swagger:
  enabled: ${SWAGGER_ENABLED:true}
  title: Gongxingxue API - 开发环境
  description: API Documentation for Exam Preparation Resource Sharing Platform (Development)
  version: 1.0.0-DEV
  contact:
    name: Gongxingxue Dev Team
    email: ${CONTACT_EMAIL:<EMAIL>}

# 日志配置 - 开发环境
logging:
  level:
    com.gongxingxue: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
