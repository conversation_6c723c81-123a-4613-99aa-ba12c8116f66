import{a as $,u as D}from"./admin-LhMRwqz-.js";import{r as m,x as H,o as j,b as q,d as k,q as A,h as _,i as T,e as a,f as l,g as i,s as G,E as f,l as c,m as o,t as d,j as w,G as V,D as J}from"./index-CWlqViBh.js";import{f as K}from"./ip-BIZqVrX-.js";import{_ as O}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Q={class:"admin-users"},W={class:"users-table"},X={class:"table-filters"},Z={__name:"Users",setup(ee){const v=m([]),b=m(!1),y=m(0),p=m(1),C=m(10),n=H({username:"",dateRange:[]}),u=async()=>{b.value=!0;try{const s={page:p.value,size:C.value,username:n.username||void 0};n.dateRange&&n.dateRange.length===2&&(s.startTime=n.dateRange[0],s.endTime=n.dateRange[1]);const t=await $(s);v.value=t.data.records,y.value=t.data.total}catch(s){console.error("Failed to fetch users:",s),f.error("获取用户列表失败")}finally{b.value=!1}},U=s=>{p.value=s,u()},R=()=>{p.value=1,u()},Y=()=>{n.username="",n.dateRange=[],p.value=1,u()},B=async s=>{try{await J.confirm(`确定要禁用用户 "${s.username}" 吗？禁用后该用户将无法登录。`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await D(s.id,1),f.success("用户已禁用"),u()}catch(t){t!=="cancel"&&(console.error("Failed to disable user:",t),f.error("操作失败"))}},M=async s=>{try{await D(s.id,0),f.success("用户已启用"),u()}catch(t){console.error("Failed to enable user:",t),f.error("操作失败")}};return j(()=>{u()}),(s,t)=>{const z=i("el-input"),h=i("el-form-item"),E=i("el-date-picker"),g=i("el-button"),I=i("el-form"),r=i("el-table-column"),x=i("el-tag"),F=i("el-table"),L=i("el-pagination"),N=i("el-empty"),S=G("loading");return c(),q("div",Q,[t[6]||(t[6]=k("h2",{class:"page-title"},"用户管理",-1)),k("div",W,[k("div",X,[a(I,{inline:!0,model:n},{default:l(()=>[a(h,{label:"用户名"},{default:l(()=>[a(z,{modelValue:n.username,"onUpdate:modelValue":t[0]||(t[0]=e=>n.username=e),placeholder:"用户名"},null,8,["modelValue"])]),_:1}),a(h,{label:"注册时间"},{default:l(()=>[a(E,{modelValue:n.dateRange,"onUpdate:modelValue":t[1]||(t[1]=e=>n.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),a(h,null,{default:l(()=>[a(g,{type:"primary",onClick:R},{default:l(()=>t[2]||(t[2]=[o("搜索")])),_:1,__:[2]}),a(g,{onClick:Y},{default:l(()=>t[3]||(t[3]=[o("重置")])),_:1,__:[3]})]),_:1})]),_:1},8,["model"])]),A((c(),_(F,{data:v.value,style:{width:"100%"}},{default:l(()=>[a(r,{prop:"username",label:"用户名","min-width":"120"}),a(r,{prop:"nickname",label:"昵称","min-width":"120"},{default:l(({row:e})=>[o(d(e.nickname||"-"),1)]),_:1}),a(r,{prop:"role",label:"角色",width:"100"},{default:l(({row:e})=>[a(x,{type:e.role===1?"danger":""},{default:l(()=>[o(d(e.role===1?"管理员":"普通用户"),1)]),_:2},1032,["type"])]),_:1}),a(r,{prop:"email",label:"邮箱","min-width":"150"},{default:l(({row:e})=>[o(d(e.email||"-"),1)]),_:1}),a(r,{prop:"phone",label:"手机号",width:"120"},{default:l(({row:e})=>[o(d(e.phone||"-"),1)]),_:1}),a(r,{prop:"registerIp",label:"注册IP",width:"120"},{default:l(({row:e})=>[o(d(w(K)(e.registerIp)),1)]),_:1}),a(r,{prop:"createTime",label:"注册时间",width:"180"},{default:l(({row:e})=>[o(d(w(V)(e.createTime)),1)]),_:1}),a(r,{prop:"lastLoginTime",label:"最后登录时间",width:"180"},{default:l(({row:e})=>[o(d(e.lastLoginTime?w(V)(e.lastLoginTime):"-"),1)]),_:1}),a(r,{prop:"status",label:"状态",width:"100"},{default:l(({row:e})=>[a(x,{type:e.status===0?"success":"danger"},{default:l(()=>[o(d(e.status===0?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),a(r,{label:"操作",width:"120",fixed:"right"},{default:l(({row:e})=>[e.status===0?(c(),_(g,{key:0,type:"danger",size:"small",onClick:P=>B(e),disabled:e.role===1},{default:l(()=>t[4]||(t[4]=[o(" 禁用 ")])),_:2,__:[4]},1032,["onClick","disabled"])):(c(),_(g,{key:1,type:"success",size:"small",onClick:P=>M(e)},{default:l(()=>t[5]||(t[5]=[o(" 启用 ")])),_:2,__:[5]},1032,["onClick"]))]),_:1})]),_:1},8,["data"])),[[S,b.value]]),y.value>0?(c(),_(L,{key:0,background:"",layout:"prev, pager, next",total:y.value,"page-size":C.value,"current-page":p.value,onCurrentChange:U,class:"pagination"},null,8,["total","page-size","current-page"])):T("",!0),v.value.length===0?(c(),_(N,{key:1,description:"暂无用户数据"})):T("",!0)])])}}},ne=O(Z,[["__scopeId","data-v-af7cb0ca"]]);export{ne as default};
