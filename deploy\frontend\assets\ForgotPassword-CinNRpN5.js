import{x,r as _,b as k,d as o,e as s,f as t,y as P,g as r,k as R,l as V,m as i,A as E,E as p}from"./index-CWlqViBh.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";const B={class:"forgot-password-page"},C={class:"forgot-password-container"},N={class:"forgot-password-footer"},S={style:{"margin-top":"10px"}},M={__name:"ForgotPassword",setup(T){const g=R(),l=x({email:""}),c={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},n=_(null),a=_(!1),d=async()=>{n.value&&await n.value.validate(async u=>{if(u){a.value=!0;try{await E(l.email),p.success("密码重置邮件已发送，请查收邮箱"),setTimeout(()=>{g.push("/login")},3e3)}catch(e){console.error("Send reset email failed:",e),p.error(e.message||"发送重置邮件失败，请稍后重试")}finally{a.value=!1}}})};return(u,e)=>{const v=r("el-input"),m=r("el-form-item"),w=r("el-button"),b=r("el-form"),f=r("router-link");return V(),k("div",B,[o("div",C,[e[6]||(e[6]=o("h2",{class:"title"},"找回密码",-1)),s(b,{ref_key:"forgotPasswordFormRef",ref:n,model:l,rules:c,"label-width":"0",onSubmit:P(d,["prevent"])},{default:t(()=>[s(m,{prop:"email"},{default:t(()=>[s(v,{modelValue:l.email,"onUpdate:modelValue":e[0]||(e[0]=y=>l.email=y),placeholder:"请输入绑定的邮箱地址","prefix-icon":"el-icon-message",disabled:a.value},null,8,["modelValue","disabled"])]),_:1}),s(m,null,{default:t(()=>[s(w,{type:"primary",loading:a.value,class:"reset-button",onClick:d},{default:t(()=>e[1]||(e[1]=[i(" 发送重置邮件 ")])),_:1,__:[1]},8,["loading"])]),_:1})]),_:1},8,["model"]),o("div",N,[o("div",null,[e[3]||(e[3]=o("span",null,"记起密码了？",-1)),s(f,{to:"/login"},{default:t(()=>e[2]||(e[2]=[i("返回登录")])),_:1,__:[2]})]),o("div",S,[e[5]||(e[5]=o("span",null,"还没有账号？",-1)),s(f,{to:"/register"},{default:t(()=>e[4]||(e[4]=[i("立即注册")])),_:1,__:[4]})])])])])}}},I=F(M,[["__scopeId","data-v-ac9388b1"]]);export{I as default};
