- "BOOT-INF/lib/spring-boot-2.7.12.jar"
- "BOOT-INF/lib/logback-classic-1.2.12.jar"
- "BOOT-INF/lib/logback-core-1.2.12.jar"
- "BOOT-INF/lib/log4j-to-slf4j-2.17.2.jar"
- "BOOT-INF/lib/log4j-api-2.17.2.jar"
- "BOOT-INF/lib/jul-to-slf4j-1.7.36.jar"
- "BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar"
- "BOOT-INF/lib/snakeyaml-1.30.jar"
- "BOOT-INF/lib/jackson-datatype-jdk8-2.13.5.jar"
- "BOOT-INF/lib/jackson-datatype-jsr310-2.13.5.jar"
- "BOOT-INF/lib/jackson-module-parameter-names-2.13.5.jar"
- "BOOT-INF/lib/tomcat-embed-core-9.0.75.jar"
- "BOOT-INF/lib/tomcat-embed-websocket-9.0.75.jar"
- "BOOT-INF/lib/spring-web-5.3.27.jar"
- "BOOT-INF/lib/spring-beans-5.3.27.jar"
- "BOOT-INF/lib/spring-webmvc-5.3.27.jar"
- "BOOT-INF/lib/spring-context-5.3.27.jar"
- "BOOT-INF/lib/spring-expression-5.3.27.jar"
- "BOOT-INF/lib/tomcat-embed-el-9.0.75.jar"
- "BOOT-INF/lib/hibernate-validator-6.2.5.Final.jar"
- "BOOT-INF/lib/jakarta.validation-api-2.0.2.jar"
- "BOOT-INF/lib/jboss-logging-3.4.3.Final.jar"
- "BOOT-INF/lib/spring-aop-5.3.27.jar"
- "BOOT-INF/lib/aspectjweaver-1.9.7.jar"
- "BOOT-INF/lib/spring-context-support-5.3.27.jar"
- "BOOT-INF/lib/jakarta.mail-1.6.7.jar"
- "BOOT-INF/lib/jakarta.activation-1.2.2.jar"
- "BOOT-INF/lib/mysql-connector-j-8.0.33.jar"
- "BOOT-INF/lib/mybatis-plus-boot-starter-3.5.3.1.jar"
- "BOOT-INF/lib/mybatis-plus-3.5.3.1.jar"
- "BOOT-INF/lib/mybatis-plus-extension-3.5.3.1.jar"
- "BOOT-INF/lib/mybatis-plus-core-3.5.3.1.jar"
- "BOOT-INF/lib/mybatis-plus-annotation-3.5.3.1.jar"
- "BOOT-INF/lib/jsqlparser-4.4.jar"
- "BOOT-INF/lib/mybatis-3.5.10.jar"
- "BOOT-INF/lib/mybatis-spring-2.0.7.jar"
- "BOOT-INF/lib/spring-boot-autoconfigure-2.7.12.jar"
- "BOOT-INF/lib/HikariCP-4.0.3.jar"
- "BOOT-INF/lib/spring-jdbc-5.3.27.jar"
- "BOOT-INF/lib/spring-tx-5.3.27.jar"
- "BOOT-INF/lib/jjwt-api-0.11.5.jar"
- "BOOT-INF/lib/jjwt-impl-0.11.5.jar"
- "BOOT-INF/lib/jjwt-jackson-0.11.5.jar"
- "BOOT-INF/lib/jackson-databind-2.13.5.jar"
- "BOOT-INF/lib/jackson-annotations-2.13.5.jar"
- "BOOT-INF/lib/jackson-core-2.13.5.jar"
- "BOOT-INF/lib/springfox-boot-starter-3.0.0.jar"
- "BOOT-INF/lib/springfox-oas-3.0.0.jar"
- "BOOT-INF/lib/swagger-annotations-2.1.2.jar"
- "BOOT-INF/lib/swagger-models-2.1.2.jar"
- "BOOT-INF/lib/springfox-spi-3.0.0.jar"
- "BOOT-INF/lib/springfox-schema-3.0.0.jar"
- "BOOT-INF/lib/springfox-core-3.0.0.jar"
- "BOOT-INF/lib/springfox-spring-web-3.0.0.jar"
- "BOOT-INF/lib/classgraph-4.8.83.jar"
- "BOOT-INF/lib/springfox-spring-webmvc-3.0.0.jar"
- "BOOT-INF/lib/springfox-spring-webflux-3.0.0.jar"
- "BOOT-INF/lib/springfox-swagger-common-3.0.0.jar"
- "BOOT-INF/lib/mapstruct-1.3.1.Final.jar"
- "BOOT-INF/lib/springfox-data-rest-3.0.0.jar"
- "BOOT-INF/lib/springfox-bean-validators-3.0.0.jar"
- "BOOT-INF/lib/springfox-swagger2-3.0.0.jar"
- "BOOT-INF/lib/swagger-annotations-1.5.20.jar"
- "BOOT-INF/lib/swagger-models-1.5.20.jar"
- "BOOT-INF/lib/springfox-swagger-ui-3.0.0.jar"
- "BOOT-INF/lib/classmate-1.5.1.jar"
- "BOOT-INF/lib/slf4j-api-1.7.36.jar"
- "BOOT-INF/lib/spring-plugin-core-2.0.0.RELEASE.jar"
- "BOOT-INF/lib/spring-plugin-metadata-2.0.0.RELEASE.jar"
- "BOOT-INF/lib/hutool-all-5.8.18.jar"
- "BOOT-INF/lib/spring-security-crypto-5.7.8.jar"
- "BOOT-INF/lib/byte-buddy-1.12.23.jar"
- "BOOT-INF/lib/spring-core-5.3.27.jar"
- "BOOT-INF/lib/spring-jcl-5.3.27.jar"
- "BOOT-INF/lib/spring-boot-jarmode-layertools-2.7.12.jar"
