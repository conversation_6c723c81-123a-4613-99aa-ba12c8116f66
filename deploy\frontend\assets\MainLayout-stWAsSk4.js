import{u as P,r as w,c as g,w as G,o as J,a as O,b as h,d as t,e as a,f as s,g as n,h as C,i as V,F as Q,t as M,j as U,k as W,l as m,m as l,n as X,E as R}from"./index-CWlqViBh.js";import{g as Z}from"./message-Dg3OniXv.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ee={class:"main-layout"},ae={class:"header"},se={class:"container"},oe={class:"header-content"},te={class:"logo"},ne={class:"search-bar"},le={class:"nav-links"},ue={class:"user-avatar"},re={class:"page-container"},de={class:"container"},ie={class:"footer"},ce={class:"container"},me={__name:"MainLayout",setup(_e){const u=W(),_=P(),k=w(null),y=w(""),d=w(0),r=g(()=>_.isLoggedIn),E=g(()=>_.isAdmin),F=g(()=>{var o;return((o=_.user)==null?void 0:o.avatar)||""}),L=g(()=>{var e;const o=((e=_.user)==null?void 0:e.nickname)||"";return o?o.charAt(0).toUpperCase():"U"}),A=new Date().getFullYear(),I=()=>{u.push({name:"SearchResults",query:{examType:k.value,keyword:y.value}})},S=()=>{r.value?u.push("/upload"):(R.warning("请先登录后再发布资料"),u.push("/login?redirect=/upload"))},B=()=>{u.push("/messages")},v=async()=>{if(!r.value){d.value=0;return}try{const o=await Z();o.success&&(d.value=o.data||0)}catch{d.value=0}},N=o=>{switch(o){case"profile":u.push({name:"Profile"});break;case"myResources":u.push({name:"MyResources"});break;case"myFavorites":u.push({name:"MyFavorites"});break;case"myComments":u.push({name:"MyComments"});break;case"admin":u.push({name:"AdminDashboard"});break;case"logout":_.logout(),R.success("已成功退出登录"),u.push({name:"Home"});break}},x=()=>{v()};return G(r,o=>{o?v():d.value=0},{immediate:!0}),J(()=>{window.addEventListener("messageRead",x),r.value&&(v(),setInterval(()=>{r.value&&v()},3e4))}),O(()=>{window.removeEventListener("messageRead",x)}),(o,e)=>{const f=n("router-link"),i=n("el-option"),T=n("el-select"),D=n("el-input"),p=n("el-button"),z=n("el-icon"),Y=n("el-badge"),j=n("el-avatar"),c=n("el-dropdown-item"),q=n("el-dropdown-menu"),H=n("el-dropdown"),K=n("router-view");return m(),h("div",ee,[t("header",ae,[t("div",se,[t("div",oe,[t("div",te,[a(f,{to:"/"},{default:s(()=>e[2]||(e[2]=[t("h1",null,"考研/考公资料共享平台",-1)])),_:1,__:[2]})]),t("div",ne,[a(T,{modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=b=>k.value=b),placeholder:"考试类型",class:"exam-type-select"},{default:s(()=>[a(i,{label:"全部",value:null}),a(i,{label:"考研",value:0}),a(i,{label:"考公",value:1}),a(i,{label:"法考",value:2}),a(i,{label:"教资",value:3}),a(i,{label:"其他",value:4})]),_:1},8,["modelValue"]),a(D,{modelValue:y.value,"onUpdate:modelValue":e[1]||(e[1]=b=>y.value=b),placeholder:"搜索资料...",class:"search-input"},null,8,["modelValue"]),a(p,{type:"primary",onClick:I},{default:s(()=>e[3]||(e[3]=[l("搜索")])),_:1,__:[3]})]),t("div",le,[a(p,{type:"success",onClick:S,class:"nav-link"},{default:s(()=>e[4]||(e[4]=[l(" 发布资料 ")])),_:1,__:[4]}),r.value?(m(),C(f,{key:0,to:"/study-plan",class:"nav-link"},{default:s(()=>[a(p,null,{default:s(()=>e[5]||(e[5]=[l("学习规划")])),_:1,__:[5]})]),_:1})):V("",!0),r.value?(m(),h("div",{key:1,class:"message-icon",onClick:B},[a(Y,{value:d.value,hidden:d.value===0,max:99},{default:s(()=>[a(z,{size:20,class:"message-bell"},{default:s(()=>[a(U(X))]),_:1})]),_:1},8,["value","hidden"])])):V("",!0),r.value?(m(),C(H,{key:2,trigger:"click",onCommand:N},{dropdown:s(()=>[a(q,null,{default:s(()=>[a(c,{command:"profile"},{default:s(()=>e[6]||(e[6]=[l("个人中心")])),_:1,__:[6]}),a(c,{command:"myResources"},{default:s(()=>e[7]||(e[7]=[l("我的资料")])),_:1,__:[7]}),a(c,{command:"myFavorites"},{default:s(()=>e[8]||(e[8]=[l("我的收藏")])),_:1,__:[8]}),a(c,{command:"myComments"},{default:s(()=>e[9]||(e[9]=[l("我的评论")])),_:1,__:[9]}),E.value?(m(),C(c,{key:0,command:"admin"},{default:s(()=>e[10]||(e[10]=[l("后台管理")])),_:1,__:[10]})):V("",!0),a(c,{divided:"",command:"logout"},{default:s(()=>e[11]||(e[11]=[l("退出登录")])),_:1,__:[11]})]),_:1})]),default:s(()=>[t("div",ue,[a(j,{size:32,src:F.value},{default:s(()=>[l(M(L.value),1)]),_:1},8,["src"])])]),_:1})):(m(),h(Q,{key:3},[a(f,{to:"/login",class:"nav-link"},{default:s(()=>[a(p,null,{default:s(()=>e[12]||(e[12]=[l("登录")])),_:1,__:[12]})]),_:1}),a(f,{to:"/register",class:"nav-link"},{default:s(()=>[a(p,null,{default:s(()=>e[13]||(e[13]=[l("注册")])),_:1,__:[13]})]),_:1})],64))])])])]),t("main",re,[t("div",de,[a(K)])]),t("footer",ie,[t("div",ce,[t("p",null,"© "+M(U(A))+" 考研/考公资料共享平台 - 版权所有",1)])])])}}},ge=$(me,[["__scopeId","data-v-b6496f85"]]);export{ge as default};
