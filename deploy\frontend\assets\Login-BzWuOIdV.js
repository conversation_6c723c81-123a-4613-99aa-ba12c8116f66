import{u as C,x as E,r as g,o as R,b as U,d as t,e as r,f as a,y as L,g as n,k as B,p as K,l as M,z as h,m as f,E as p}from"./index-CWlqViBh.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"login-page"},D={class:"login-container"},F={class:"captcha-container"},$=["src"],z={class:"login-footer"},A={style:{"margin-top":"10px"}},I={__name:"Login",setup(T){const y=B(),x=K(),V=C(),s=E({username:"",password:"",captcha:""}),b={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:4,message:"验证码为4位字符",trigger:"blur"}]},c=g(null),d=g(!1),_=g(""),m=()=>{_.value=`/api/auth/captcha?t=${Date.now()}`},i=async()=>{c.value&&await c.value.validate(async w=>{var e;if(w){d.value=!0;try{console.log("Attempting login with:",{username:s.username,password:s.password.replace(/./g,"*")}),await V.login(s.username,s.password,s.captcha),p.success("登录成功");const o=x.query.redirect||"/";y.replace(o)}catch(o){console.error("Login failed:",o),m(),s.captcha="",o.response?(console.error("Error response:",o.response),p.error(`登录失败: ${((e=o.response.data)==null?void 0:e.message)||o.message||"请检查用户名和密码"}`)):o.request?(console.error("Error request:",o.request),p.error("网络请求失败，请检查网络连接")):p.error(o.message||"登录失败，请检查用户名和密码")}finally{d.value=!1}}})};return R(()=>{m()}),(w,e)=>{const o=n("el-input"),u=n("el-form-item"),k=n("el-button"),q=n("el-form"),v=n("router-link");return M(),U("div",S,[t("div",D,[e[7]||(e[7]=t("h2",{class:"title"},"用户登录",-1)),r(q,{ref_key:"loginFormRef",ref:c,model:s,rules:b,"label-width":"0",onSubmit:L(i,["prevent"])},{default:a(()=>[r(u,{prop:"username"},{default:a(()=>[r(o,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=l=>s.username=l),placeholder:"用户名","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),r(u,{prop:"password"},{default:a(()=>[r(o,{modelValue:s.password,"onUpdate:modelValue":e[1]||(e[1]=l=>s.password=l),type:"password",placeholder:"密码","prefix-icon":"el-icon-lock","show-password":"",onKeyup:h(i,["enter"])},null,8,["modelValue"])]),_:1}),r(u,{prop:"captcha"},{default:a(()=>[t("div",F,[r(o,{modelValue:s.captcha,"onUpdate:modelValue":e[2]||(e[2]=l=>s.captcha=l),placeholder:"验证码","prefix-icon":"el-icon-picture",onKeyup:h(i,["enter"]),style:{flex:"1"}},null,8,["modelValue"]),t("img",{src:_.value,onClick:m,class:"captcha-image",title:"点击刷新验证码",alt:"验证码"},null,8,$)])]),_:1}),r(u,null,{default:a(()=>[r(k,{type:"primary",loading:d.value,class:"login-button",onClick:i},{default:a(()=>e[3]||(e[3]=[f(" 登录 ")])),_:1,__:[3]},8,["loading"])]),_:1})]),_:1},8,["model"]),t("div",z,[t("div",null,[e[5]||(e[5]=t("span",null,"还没有账号？",-1)),r(v,{to:"/register"},{default:a(()=>e[4]||(e[4]=[f("立即注册")])),_:1,__:[4]})]),t("div",A,[r(v,{to:"/forgot-password"},{default:a(()=>e[6]||(e[6]=[f("忘记密码？")])),_:1,__:[6]})])])])])}}},H=N(I,[["__scopeId","data-v-8bd3fa45"]]);export{H as default};
