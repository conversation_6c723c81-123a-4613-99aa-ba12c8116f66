import{g as Ye,c as Fe,d as Ue,e as ze,b as ie,r as Me,f as $e}from"./admin-LhMRwqz-.js";import{r as h,u as Be,x as te,w as oe,o as Ie,b as U,d as X,e,f as t,g as y,E as T,s as Oe,l as b,q as ne,h as C,i as D,m as u,t as f,H as ue,j as L,G,D as se}from"./index-CWlqViBh.js";import{_ as Ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Ae={class:"admin-resources"},Le={class:"resources-table"},Ge={class:"table-filters"},He={class:"table-actions"},Ne={key:0,class:"selection-actions"},qe={class:"refresh-action"},Ke={class:"resources-table"},Je={class:"table-filters"},Qe={class:"resources-table"},We={class:"table-filters"},Ze={class:"reject-reason"},el={key:1,class:"no-reason"},ll={__name:"Resources",setup(al){const j=h("pending"),k=h([]),z=h(!1),V=h(0),w=h(1),Y=h(10),x=h([]);Be();const r=te({examType:null,fileType:null,username:"",keyword:"",timeType:"upload",timeRange:null,customTimeRange:null}),p=te({examType:null,fileType:null,username:"",keyword:"",timeRange:null,customTimeRange:null}),d=te({examType:null,fileType:null,username:"",keyword:"",timeType:"upload",timeRange:null,customTimeRange:null}),M=h(!1),$=h(""),re=h(null),B=h(!1),I=h(""),F=h(!1),_=async()=>{z.value=!0;try{let n;if(j.value==="pending"){const l=N(p.timeRange,p.customTimeRange,"upload"),o={page:w.value,size:Y.value,examType:p.examType,fileType:p.fileType,username:p.username,keyword:p.keyword,...l};console.log("Pending resources params:",o),n=await Ye(o)}else if(j.value==="approved"){const l=N(r.timeRange,r.customTimeRange,r.timeType),o={page:w.value,size:Y.value,examType:r.examType,fileType:r.fileType,username:r.username,keyword:r.keyword,...l};console.log("Approved resources params:",o),n=await Fe(o)}else if(j.value==="rejected"){const l=N(d.timeRange,d.customTimeRange,d.timeType),o={page:w.value,size:Y.value,examType:d.examType,fileType:d.fileType,username:d.username,keyword:d.keyword,...l};console.log("Rejected resources params:",o),n=await Ue(o)}k.value=n.data.records||[],V.value=n.data.total||0}catch(n){console.error("Failed to fetch resources:",n),T.error("获取资料列表失败"),k.value=[],V.value=0}finally{z.value=!1}},pe=n=>{j.value=n.props.name,w.value=1,x.value=[],k.value=[],V.value=0,_()},me=()=>{x.value=[],k.value=[],V.value=0,_()},H=n=>{w.value=n,_()},ce=n=>{x.value=n},fe=()=>{w.value=1,_()},ve=()=>{r.examType=null,r.fileType=null,r.username="",r.keyword="",r.timeType="upload",r.timeRange=null,r.customTimeRange=null,w.value=1,_()},ge=()=>{w.value=1,_()},ye=()=>{p.examType=null,p.fileType=null,p.username="",p.keyword="",p.timeRange=null,p.customTimeRange=null,w.value=1,_()},be=()=>{w.value=1,_()},_e=()=>{d.examType=null,d.fileType=null,d.username="",d.keyword="",d.timeType="upload",d.timeRange=null,d.customTimeRange=null,w.value=1,_()},N=(n,l,o)=>{if(!n)return{};const c=new Date;let s,v;switch(n){case"today":s=new Date(c.getFullYear(),c.getMonth(),c.getDate(),0,0,0),v=new Date(c.getFullYear(),c.getMonth(),c.getDate(),23,59,59,999);break;case"yesterday":const g=new Date(c.getTime()-24*60*60*1e3);s=new Date(g.getFullYear(),g.getMonth(),g.getDate(),0,0,0),v=new Date(g.getFullYear(),g.getMonth(),g.getDate(),23,59,59,999);break;case"week":s=new Date(c.getTime()-7*24*60*60*1e3),v=c;break;case"month":s=new Date(c.getTime()-30*24*60*60*1e3),v=c;break;case"custom":if(l&&Array.isArray(l)&&l.length===2)s=new Date(l[0]+" 00:00:00"),v=new Date(l[1]+" 23:59:59"),console.log("Custom date range:",l,"Expanded to:",{startTime:s,endTime:v});else return console.log("Invalid custom date range:",l),{};break;default:return{}}if(!s||!v)return{};const R=g=>{const W=g.getFullYear(),Z=String(g.getMonth()+1).padStart(2,"0"),i=String(g.getDate()).padStart(2,"0"),P=String(g.getHours()).padStart(2,"0"),E=String(g.getMinutes()).padStart(2,"0"),A=String(g.getSeconds()).padStart(2,"0");return`${W}-${Z}-${i} ${P}:${E}:${A}`},m={};return o==="upload"?(m.startTime=R(s),m.endTime=R(v)):o==="audit"&&(m.auditStartTime=R(s),m.auditEndTime=R(v)),console.log("Time range params:",{timeRange:n,timeType:o,startTime:R(s),endTime:R(v),params:m}),m},Te=n=>{n!=="custom"&&(r.customTimeRange=null)},we=n=>{console.log("Pending time range changed to:",n),n!=="custom"&&(p.customTimeRange=null,console.log("Cleared custom date range"))},he=n=>{console.log("Rejected time range changed to:",n),n!=="custom"&&(d.customTimeRange=null,console.log("Cleared rejected custom date range"))};oe(()=>p.customTimeRange,n=>{console.log("Pending custom date range changed:",n)}),oe(()=>r.customTimeRange,n=>{console.log("Approved custom date range changed:",n)}),oe(()=>d.customTimeRange,n=>{console.log("Rejected custom date range changed:",n)});const q=n=>["考研","考公","法考","教资","其他"][n]||"未知",K=n=>["primary","success","warning","danger",""][n]||"",J=n=>{if(!n)return"未知";const l=n.lastIndexOf(".");return l===-1?"无扩展名":n.substring(l+1).toUpperCase()},Q=n=>{if(!n||n===0)return"未知";const l=["B","KB","MB","GB"];let o=n,c=0;for(;o>=1024&&c<l.length-1;)o/=1024,c++;return`${o.toFixed(1)} ${l[c]}`},xe=async n=>{try{await se.confirm(`确定要通过资料 '${n.name}' 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),await ie(n.id),T.success("资料已通过审核"),_()}catch(l){l!=="cancel"&&(console.error("Failed to approve resource:",l),T.error("操作失败"))}},ke=n=>{re.value=n,$.value="",M.value=!0},Ve=async()=>{if(!$.value.trim()){T.warning("请输入驳回原因");return}F.value=!0;try{await Me(re.value.id,$.value),T.success("资料已驳回"),M.value=!1,_()}catch(n){console.error("Failed to reject resource:",n),T.error("操作失败")}finally{F.value=!1}},Re=async()=>{if(x.value.length===0){T.warning("请选择要批量通过的资料");return}try{await se.confirm(`确定要批量通过选中的 ${x.value.length} 个资料吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const n=x.value.map(l=>l.id);await ze(n),T.success("批量通过操作成功"),_()}catch(n){n!=="cancel"&&(console.error("Failed to batch approve resources:",n),T.error("操作失败"))}},Ce=()=>{if(x.value.length===0){T.warning("请选择要批量驳回的资料");return}I.value="",B.value=!0},De=async()=>{if(!I.value.trim()){T.warning("请输入驳回原因");return}F.value=!0;try{const n=x.value.map(l=>l.id);await $e(n,I.value),T.success("批量驳回操作成功"),B.value=!1,_()}catch(n){console.error("Failed to batch reject resources:",n),T.error("操作失败")}finally{F.value=!1}},Pe=async n=>{try{await se.confirm(`确定要重新通过资料 '${n.name}' 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),await ie(n.id),T.success("资料已重新通过审核"),_()}catch(l){l!=="cancel"&&(console.error("Failed to re-approve resource:",l),T.error("操作失败"))}},O=n=>{const l=`/api/admin/resources/preview/${n.id}`;window.open(l,"_blank")};return Ie(()=>{_()}),(n,l)=>{const o=y("el-option"),c=y("el-select"),s=y("el-form-item"),v=y("el-input"),R=y("el-date-picker"),m=y("el-button"),g=y("el-form"),W=y("el-icon-refresh"),Z=y("el-icon"),i=y("el-table-column"),P=y("el-tag"),E=y("el-table"),A=y("el-pagination"),ee=y("el-empty"),le=y("el-tab-pane"),Se=y("router-link"),Xe=y("el-tooltip"),je=y("el-tabs"),de=y("el-dialog"),ae=Oe("loading");return b(),U("div",Ae,[l[44]||(l[44]=X("h2",{class:"page-title"},"资料管理",-1)),e(je,{modelValue:j.value,"onUpdate:modelValue":l[20]||(l[20]=a=>j.value=a),onTabClick:pe},{default:t(()=>[e(le,{label:"待审核资料",name:"pending"},{default:t(()=>[ne((b(),U("div",Le,[X("div",Ge,[e(g,{inline:!0,model:p},{default:t(()=>[e(s,{label:"考试类型"},{default:t(()=>[e(c,{modelValue:p.examType,"onUpdate:modelValue":l[0]||(l[0]=a=>p.examType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"考研",value:0}),e(o,{label:"考公",value:1}),e(o,{label:"法考",value:2}),e(o,{label:"教资",value:3}),e(o,{label:"其他",value:4})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"文件类型"},{default:t(()=>[e(c,{modelValue:p.fileType,"onUpdate:modelValue":l[1]||(l[1]=a=>p.fileType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"PDF",value:"PDF"}),e(o,{label:"DOC",value:"DOC"}),e(o,{label:"DOCX",value:"DOCX"}),e(o,{label:"PPT",value:"PPT"}),e(o,{label:"PPTX",value:"PPTX"}),e(o,{label:"XLS",value:"XLS"}),e(o,{label:"XLSX",value:"XLSX"}),e(o,{label:"TXT",value:"TXT"}),e(o,{label:"图片",value:"IMAGE"}),e(o,{label:"其他",value:"OTHER"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"上传用户"},{default:t(()=>[e(v,{modelValue:p.username,"onUpdate:modelValue":l[2]||(l[2]=a=>p.username=a),placeholder:"用户名",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"关键词"},{default:t(()=>[e(v,{modelValue:p.keyword,"onUpdate:modelValue":l[3]||(l[3]=a=>p.keyword=a),placeholder:"资料名称",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"上传时间"},{default:t(()=>[e(c,{modelValue:p.timeRange,"onUpdate:modelValue":l[4]||(l[4]=a=>p.timeRange=a),placeholder:"选择时间范围",clearable:"",style:{width:"150px"},onChange:we},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"今天",value:"today"}),e(o,{label:"昨天",value:"yesterday"}),e(o,{label:"最近7天",value:"week"}),e(o,{label:"最近30天",value:"month"}),e(o,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),p.timeRange==="custom"?(b(),C(s,{key:0,label:"自定义日期"},{default:t(()=>[e(R,{modelValue:p.customTimeRange,"onUpdate:modelValue":l[5]||(l[5]=a=>p.customTimeRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"300px"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})):D("",!0),e(s,null,{default:t(()=>[e(m,{type:"primary",onClick:ge},{default:t(()=>l[27]||(l[27]=[u("搜索")])),_:1,__:[27]}),e(m,{onClick:ye},{default:t(()=>l[28]||(l[28]=[u("重置")])),_:1,__:[28]})]),_:1})]),_:1},8,["model"])]),X("div",He,[x.value.length>0?(b(),U("div",Ne,[e(m,{type:"success",onClick:Re},{default:t(()=>[u(" 批量通过 ("+f(x.value.length)+") ",1)]),_:1}),e(m,{type:"danger",onClick:Ce},{default:t(()=>[u(" 批量驳回 ("+f(x.value.length)+") ",1)]),_:1})])):D("",!0),X("div",qe,[e(m,{type:"primary",plain:"",onClick:me},{default:t(()=>[e(Z,null,{default:t(()=>[e(W)]),_:1}),l[29]||(l[29]=u(" 刷新 "))]),_:1,__:[29]})])]),e(E,{data:k.value,style:{width:"100%"},onSelectionChange:ce},{default:t(()=>[e(i,{type:"selection",width:"55"}),e(i,{prop:"name",label:"资料名称","min-width":"200"},{default:t(({row:a})=>[e(m,{type:"text",onClick:S=>O(a)},{default:t(()=>[u(f(a.name),1)]),_:2},1032,["onClick"])]),_:1}),e(i,{prop:"examType",label:"考试类型",width:"100"},{default:t(({row:a})=>[e(P,{type:K(a.examType),class:ue({"custom-purple":a.examType===4})},{default:t(()=>[u(f(q(a.examType)),1)]),_:2},1032,["type","class"])]),_:1}),e(i,{prop:"fileType",label:"文件类型",width:"100"},{default:t(({row:a})=>[e(P,{type:"info",size:"small"},{default:t(()=>[u(f(J(a.originalFilename)),1)]),_:2},1024)]),_:1}),e(i,{prop:"fileSize",label:"文件大小",width:"100"},{default:t(({row:a})=>[u(f(Q(a.fileSize)),1)]),_:1}),e(i,{prop:"userId",label:"上传用户",width:"120"},{default:t(({row:a})=>[u(f(a.username||`用户 #${a.userId}`),1)]),_:1}),e(i,{prop:"createTime",label:"上传时间",width:"180"},{default:t(({row:a})=>[u(f(L(G)(a.createTime)),1)]),_:1}),e(i,{label:"操作",width:"220",fixed:"right"},{default:t(({row:a})=>[e(m,{type:"success",size:"small",onClick:S=>xe(a)},{default:t(()=>l[30]||(l[30]=[u(" 通过 ")])),_:2,__:[30]},1032,["onClick"]),e(m,{type:"danger",size:"small",onClick:S=>ke(a)},{default:t(()=>l[31]||(l[31]=[u(" 驳回 ")])),_:2,__:[31]},1032,["onClick"]),e(m,{type:"primary",size:"small",onClick:S=>O(a)},{default:t(()=>l[32]||(l[32]=[u(" 预览 ")])),_:2,__:[32]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),V.value>0?(b(),C(A,{key:0,background:"",layout:"prev, pager, next",total:V.value,"page-size":Y.value,"current-page":w.value,onCurrentChange:H,class:"pagination"},null,8,["total","page-size","current-page"])):D("",!0),k.value.length===0?(b(),C(ee,{key:1,description:"暂无待审核资料"})):D("",!0)])),[[ae,z.value]])]),_:1}),e(le,{label:"已通过资料",name:"approved"},{default:t(()=>[ne((b(),U("div",Ke,[X("div",Je,[e(g,{inline:!0,model:r},{default:t(()=>[e(s,{label:"考试类型"},{default:t(()=>[e(c,{modelValue:r.examType,"onUpdate:modelValue":l[6]||(l[6]=a=>r.examType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"考研",value:0}),e(o,{label:"考公",value:1}),e(o,{label:"法考",value:2}),e(o,{label:"教资",value:3}),e(o,{label:"其他",value:4})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"文件类型"},{default:t(()=>[e(c,{modelValue:r.fileType,"onUpdate:modelValue":l[7]||(l[7]=a=>r.fileType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"PDF",value:"PDF"}),e(o,{label:"DOC",value:"DOC"}),e(o,{label:"DOCX",value:"DOCX"}),e(o,{label:"PPT",value:"PPT"}),e(o,{label:"PPTX",value:"PPTX"}),e(o,{label:"XLS",value:"XLS"}),e(o,{label:"XLSX",value:"XLSX"}),e(o,{label:"TXT",value:"TXT"}),e(o,{label:"图片",value:"IMAGE"}),e(o,{label:"其他",value:"OTHER"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"上传用户"},{default:t(()=>[e(v,{modelValue:r.username,"onUpdate:modelValue":l[8]||(l[8]=a=>r.username=a),placeholder:"用户名",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"关键词"},{default:t(()=>[e(v,{modelValue:r.keyword,"onUpdate:modelValue":l[9]||(l[9]=a=>r.keyword=a),placeholder:"资料名称",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"时间筛选"},{default:t(()=>[e(c,{modelValue:r.timeType,"onUpdate:modelValue":l[10]||(l[10]=a=>r.timeType=a),placeholder:"时间类型",style:{width:"120px"}},{default:t(()=>[e(o,{label:"上传时间",value:"upload"}),e(o,{label:"审核时间",value:"audit"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"时间范围"},{default:t(()=>[e(c,{modelValue:r.timeRange,"onUpdate:modelValue":l[11]||(l[11]=a=>r.timeRange=a),placeholder:"选择时间范围",clearable:"",style:{width:"150px"},onChange:Te},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"今天",value:"today"}),e(o,{label:"昨天",value:"yesterday"}),e(o,{label:"最近7天",value:"week"}),e(o,{label:"最近30天",value:"month"}),e(o,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),r.timeRange==="custom"?(b(),C(s,{key:0,label:"自定义日期"},{default:t(()=>[e(R,{modelValue:r.customTimeRange,"onUpdate:modelValue":l[12]||(l[12]=a=>r.customTimeRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"300px"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})):D("",!0),e(s,null,{default:t(()=>[e(m,{type:"primary",onClick:fe},{default:t(()=>l[33]||(l[33]=[u("搜索")])),_:1,__:[33]}),e(m,{onClick:ve},{default:t(()=>l[34]||(l[34]=[u("重置")])),_:1,__:[34]})]),_:1})]),_:1},8,["model"])]),e(E,{data:k.value,style:{width:"100%"}},{default:t(()=>[e(i,{prop:"name",label:"资料名称","min-width":"200"},{default:t(({row:a})=>[e(Se,{to:{name:"ResourceDetail",params:{id:a.id}},class:"resource-link"},{default:t(()=>[u(f(a.name),1)]),_:2},1032,["to"])]),_:1}),e(i,{prop:"examType",label:"考试类型",width:"100"},{default:t(({row:a})=>[e(P,{type:K(a.examType),class:ue({"custom-purple":a.examType===4})},{default:t(()=>[u(f(q(a.examType)),1)]),_:2},1032,["type","class"])]),_:1}),e(i,{prop:"fileType",label:"文件类型",width:"100"},{default:t(({row:a})=>[e(P,{type:"info",size:"small"},{default:t(()=>[u(f(J(a.originalFilename)),1)]),_:2},1024)]),_:1}),e(i,{prop:"fileSize",label:"文件大小",width:"100"},{default:t(({row:a})=>[u(f(Q(a.fileSize)),1)]),_:1}),e(i,{prop:"userId",label:"上传用户",width:"120"},{default:t(({row:a})=>[u(f(a.username||`用户 #${a.userId}`),1)]),_:1}),e(i,{prop:"createTime",label:"上传时间",width:"180"},{default:t(({row:a})=>[u(f(L(G)(a.createTime)),1)]),_:1}),e(i,{prop:"downloadCount",label:"下载量",width:"100"}),e(i,{prop:"commentCount",label:"评论数",width:"100"}),e(i,{prop:"auditTime",label:"审核时间",width:"180"},{default:t(({row:a})=>[u(f(L(G)(a.auditTime)),1)]),_:1}),e(i,{label:"操作",width:"120",fixed:"right"},{default:t(({row:a})=>[e(m,{type:"primary",size:"small",onClick:S=>O(a)},{default:t(()=>l[35]||(l[35]=[u(" 预览 ")])),_:2,__:[35]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),V.value>0?(b(),C(A,{key:0,background:"",layout:"prev, pager, next",total:V.value,"page-size":Y.value,"current-page":w.value,onCurrentChange:H,class:"pagination"},null,8,["total","page-size","current-page"])):D("",!0),k.value.length===0?(b(),C(ee,{key:1,description:"暂无已通过资料"})):D("",!0)])),[[ae,z.value]])]),_:1}),e(le,{label:"已驳回资料",name:"rejected"},{default:t(()=>[ne((b(),U("div",Qe,[X("div",We,[e(g,{inline:!0,model:d},{default:t(()=>[e(s,{label:"考试类型"},{default:t(()=>[e(c,{modelValue:d.examType,"onUpdate:modelValue":l[13]||(l[13]=a=>d.examType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"考研",value:0}),e(o,{label:"考公",value:1}),e(o,{label:"法考",value:2}),e(o,{label:"教资",value:3}),e(o,{label:"其他",value:4})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"文件类型"},{default:t(()=>[e(c,{modelValue:d.fileType,"onUpdate:modelValue":l[14]||(l[14]=a=>d.fileType=a),placeholder:"全部",clearable:"",style:{width:"120px"}},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"PDF",value:"PDF"}),e(o,{label:"DOC",value:"DOC"}),e(o,{label:"DOCX",value:"DOCX"}),e(o,{label:"PPT",value:"PPT"}),e(o,{label:"PPTX",value:"PPTX"}),e(o,{label:"XLS",value:"XLS"}),e(o,{label:"XLSX",value:"XLSX"}),e(o,{label:"TXT",value:"TXT"}),e(o,{label:"图片",value:"IMAGE"}),e(o,{label:"其他",value:"OTHER"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"上传用户"},{default:t(()=>[e(v,{modelValue:d.username,"onUpdate:modelValue":l[15]||(l[15]=a=>d.username=a),placeholder:"用户名",style:{width:"150px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"关键词"},{default:t(()=>[e(v,{modelValue:d.keyword,"onUpdate:modelValue":l[16]||(l[16]=a=>d.keyword=a),placeholder:"资料名称",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),e(s,{label:"时间筛选"},{default:t(()=>[e(c,{modelValue:d.timeType,"onUpdate:modelValue":l[17]||(l[17]=a=>d.timeType=a),placeholder:"时间类型",style:{width:"120px"}},{default:t(()=>[e(o,{label:"上传时间",value:"upload"}),e(o,{label:"审核时间",value:"audit"})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"时间范围"},{default:t(()=>[e(c,{modelValue:d.timeRange,"onUpdate:modelValue":l[18]||(l[18]=a=>d.timeRange=a),placeholder:"选择时间范围",clearable:"",style:{width:"150px"},onChange:he},{default:t(()=>[e(o,{label:"全部",value:null}),e(o,{label:"今天",value:"today"}),e(o,{label:"昨天",value:"yesterday"}),e(o,{label:"最近7天",value:"week"}),e(o,{label:"最近30天",value:"month"}),e(o,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"])]),_:1}),d.timeRange==="custom"?(b(),C(s,{key:0,label:"自定义日期"},{default:t(()=>[e(R,{modelValue:d.customTimeRange,"onUpdate:modelValue":l[19]||(l[19]=a=>d.customTimeRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"300px"},format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",clearable:""},null,8,["modelValue"])]),_:1})):D("",!0),e(s,null,{default:t(()=>[e(m,{type:"primary",onClick:be},{default:t(()=>l[36]||(l[36]=[u("搜索")])),_:1,__:[36]}),e(m,{onClick:_e},{default:t(()=>l[37]||(l[37]=[u("重置")])),_:1,__:[37]})]),_:1})]),_:1},8,["model"])]),e(E,{data:k.value,style:{width:"100%"}},{default:t(()=>[e(i,{prop:"name",label:"资料名称","min-width":"200"},{default:t(({row:a})=>[e(m,{type:"text",onClick:S=>O(a)},{default:t(()=>[u(f(a.name),1)]),_:2},1032,["onClick"])]),_:1}),e(i,{prop:"examType",label:"考试类型",width:"100"},{default:t(({row:a})=>[e(P,{type:K(a.examType),class:ue({"custom-purple":a.examType===4})},{default:t(()=>[u(f(q(a.examType)),1)]),_:2},1032,["type","class"])]),_:1}),e(i,{prop:"fileType",label:"文件类型",width:"100"},{default:t(({row:a})=>[e(P,{type:"info",size:"small"},{default:t(()=>[u(f(J(a.originalFilename)),1)]),_:2},1024)]),_:1}),e(i,{prop:"fileSize",label:"文件大小",width:"100"},{default:t(({row:a})=>[u(f(Q(a.fileSize)),1)]),_:1}),e(i,{prop:"userId",label:"上传用户",width:"120"},{default:t(({row:a})=>[u(f(a.username||`用户 #${a.userId}`),1)]),_:1}),e(i,{prop:"createTime",label:"上传时间",width:"180"},{default:t(({row:a})=>[u(f(L(G)(a.createTime)),1)]),_:1}),e(i,{prop:"auditTime",label:"审核时间",width:"180"},{default:t(({row:a})=>[u(f(L(G)(a.auditTime)),1)]),_:1}),e(i,{prop:"rejectReason",label:"驳回原因","min-width":"200"},{default:t(({row:a})=>[a.rejectReason?(b(),C(Xe,{key:0,content:a.rejectReason,placement:"top",effect:"dark"},{default:t(()=>[X("span",Ze,f(a.rejectReason.length>20?a.rejectReason.substring(0,20)+"...":a.rejectReason),1)]),_:2},1032,["content"])):(b(),U("span",el,"未提供原因"))]),_:1}),e(i,{label:"操作",width:"180",fixed:"right"},{default:t(({row:a})=>[e(m,{type:"primary",size:"small",onClick:S=>O(a)},{default:t(()=>l[38]||(l[38]=[u(" 预览 ")])),_:2,__:[38]},1032,["onClick"]),e(m,{type:"success",size:"small",onClick:S=>Pe(a)},{default:t(()=>l[39]||(l[39]=[u(" 重新通过 ")])),_:2,__:[39]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),V.value>0?(b(),C(A,{key:0,background:"",layout:"prev, pager, next",total:V.value,"page-size":Y.value,"current-page":w.value,onCurrentChange:H,class:"pagination"},null,8,["total","page-size","current-page"])):D("",!0),k.value.length===0?(b(),C(ee,{key:1,description:"暂无已驳回资料"})):D("",!0)])),[[ae,z.value]])]),_:1})]),_:1},8,["modelValue"]),e(de,{modelValue:M.value,"onUpdate:modelValue":l[23]||(l[23]=a=>M.value=a),title:"驳回资料",width:"500px"},{footer:t(()=>[e(m,{onClick:l[22]||(l[22]=a=>M.value=!1)},{default:t(()=>l[40]||(l[40]=[u("取消")])),_:1,__:[40]}),e(m,{type:"primary",loading:F.value,onClick:Ve},{default:t(()=>l[41]||(l[41]=[u(" 确认驳回 ")])),_:1,__:[41]},8,["loading"])]),default:t(()=>[e(g,null,{default:t(()=>[e(s,{label:"驳回原因",required:""},{default:t(()=>[e(v,{modelValue:$.value,"onUpdate:modelValue":l[21]||(l[21]=a=>$.value=a),type:"textarea",rows:3,placeholder:"请输入驳回原因"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e(de,{modelValue:B.value,"onUpdate:modelValue":l[26]||(l[26]=a=>B.value=a),title:"批量驳回资料",width:"500px"},{footer:t(()=>[e(m,{onClick:l[25]||(l[25]=a=>B.value=!1)},{default:t(()=>l[42]||(l[42]=[u("取消")])),_:1,__:[42]}),e(m,{type:"primary",loading:F.value,onClick:De},{default:t(()=>l[43]||(l[43]=[u(" 确认驳回 ")])),_:1,__:[43]},8,["loading"])]),default:t(()=>[e(g,null,{default:t(()=>[e(s,{label:"驳回原因",required:""},{default:t(()=>[e(v,{modelValue:I.value,"onUpdate:modelValue":l[24]||(l[24]=a=>I.value=a),type:"textarea",rows:3,placeholder:"请输入驳回原因"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},ul=Ee(ll,[["__scopeId","data-v-f76667e9"]]);export{ul as default};
