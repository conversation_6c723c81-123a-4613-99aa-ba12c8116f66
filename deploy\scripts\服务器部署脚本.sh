#!/bin/bash

# 🚀 服务器部署脚本
# 请在服务器上运行此脚本

echo "🚀 开始部署共行学项目..."

# ========================================
# 🔧 请修改以下配置为您的实际信息
# ========================================

# 数据库配置
DB_PASSWORD="gxxdb123456"
DB_USERNAME="gongxingxue"
DB_NAME="gongxingxue"

# 邮箱配置
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="kwrhhpdvjbvldgch"

# 应用配置
SERVER_IP="************"  # 您的服务器IP
DOMAIN_NAME=""  # 暂时不使用域名

# JWT密钥（自动生成，无需修改）
JWT_SECRET=$(openssl rand -base64 64)

# ========================================
# 以下部分通常不需要修改
# ========================================

echo "📦 安装必要软件..."

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Java 8
sudo apt install -y openjdk-8-jdk

# 安装MySQL
sudo apt install -y mysql-server

# 安装Nginx（可选）
sudo apt install -y nginx

echo "🗄️ 配置数据库..."

# 配置MySQL
sudo mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER IF NOT EXISTS '$DB_USERNAME'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
sudo mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USERNAME'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

echo "📁 创建应用目录..."

# 创建应用目录
sudo mkdir -p /app/{uploads,logs,backup}
sudo chown -R $USER:$USER /app

echo "🔧 设置环境变量..."

# 创建环境变量文件
cat > /app/.env << EOF
# 生产环境配置
export SPRING_PROFILES_ACTIVE=prod
export DB_URL=*********************************************************************************************************
export DB_USERNAME=$DB_USERNAME
export DB_PASSWORD=$DB_PASSWORD
export MAIL_HOST=smtp.qq.com
export MAIL_PORT=587
export MAIL_USERNAME=$MAIL_USERNAME
export MAIL_PASSWORD=$MAIL_PASSWORD
export JWT_SECRET=$JWT_SECRET
export CORS_ALLOWED_ORIGINS=http://$SERVER_IP,https://$DOMAIN_NAME
export FRONTEND_URL=http://$SERVER_IP
export FILE_UPLOAD_DIR=/app/uploads
export LOG_FILE=/app/logs/application.log
export SERVER_PORT=8081
EOF

echo "🚀 创建启动脚本..."

# 创建启动脚本
cat > /app/start.sh << 'EOF'
#!/bin/bash
cd /app
source .env

echo "启动共行学应用..."
echo "环境: $SPRING_PROFILES_ACTIVE"
echo "端口: $SERVER_PORT"

nohup java -jar gongxingxue-backend-*.jar > startup.log 2>&1 &
echo "应用已启动，PID: $!"
EOF

chmod +x /app/start.sh

echo "🔥 配置防火墙..."

# 配置防火墙
sudo ufw allow 22     # SSH
sudo ufw allow 80     # HTTP
sudo ufw allow 443    # HTTPS
sudo ufw --force enable

echo "✅ 服务器配置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 上传 gongxingxue-backend-*.jar 到 /app/ 目录"
echo "2. 导入数据库文件"
echo "3. 运行 /app/start.sh 启动应用"
echo ""
echo "🔧 如需修改配置，请编辑 /app/.env 文件"
