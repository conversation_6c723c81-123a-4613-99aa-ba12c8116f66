# 测试环境配置
spring:
  # 数据库配置 - 测试环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:*****************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      # 测试环境连接池配置（中等）
      minimum-idle: ${DB_MIN_IDLE:10}
      maximum-pool-size: ${DB_MAX_POOL_SIZE:30}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
      pool-name: GongxingxueHikariPool-Test

  # 邮件配置 - 测试环境（可以使用假的SMTP服务）
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:test_password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

# 文件存储配置 - 测试环境
file:
  upload-dir: ${FILE_UPLOAD_DIR:./uploads/test}

# JWT配置 - 测试环境
jwt:
  secret: ${JWT_SECRET:gongxingxue_test_secret_key_for_jwt_token_generation}
  expiration: ${JWT_EXPIRATION:3600000}  # 1小时（测试环境短一些）

# Swagger配置 - 测试环境启用
swagger:
  enabled: ${SWAGGER_ENABLED:true}
  title: Gongxingxue API - 测试环境
  description: API Documentation for Exam Preparation Resource Sharing Platform (Testing)
  version: 1.0.0-TEST
  contact:
    name: Gongxingxue Test Team
    email: ${CONTACT_EMAIL:<EMAIL>}

# 日志配置 - 测试环境
logging:
  level:
    com.gongxingxue: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:./logs/test-application.log}
