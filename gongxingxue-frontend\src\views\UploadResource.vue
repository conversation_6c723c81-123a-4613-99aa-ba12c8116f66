<template>
  <div class="upload-resource">
    <!-- Full screen loading -->
    <LoadingSpinner
      v-if="uploading"
      :full-screen="true"
      size="large"
      text="正在上传文件，请稍候..."
    />

    <h2 class="page-title">上传资料</h2>

    <div class="upload-form-container">
      <el-form
        ref="uploadFormRef"
        :model="uploadForm"
        :rules="uploadRules"
        label-width="100px"
      >
        <el-form-item label="资料名称" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入资料名称（不超过50字）" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="考试类型" prop="examType">
          <el-select v-model="uploadForm.examType" placeholder="请选择考试类型">
            <el-option label="考研" :value="0" />
            <el-option label="考公" :value="1" />
            <el-option label="法考" :value="2" />
            <el-option label="教资" :value="3" />
            <el-option label="其他" :value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="资料简介" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入资料简介（不超过200字）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="上传文件" prop="file">
          <el-upload
            class="upload-file"
            :action="uploadAction"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            :multiple="false"
            accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.7z,.jpg,.jpeg,.png,.mp4,.mp3"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                支持PDF/Word/Excel/PPT/视频/音频等常见格式，单文件不超过30MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="uploading" @click="handleSimpleUpload">上传资料</el-button>
          <el-button @click="$router.push('/')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="upload-notice">
      <h3>上传须知</h3>
      <ul>
        <li>上传的资料需经过管理员审核后才能显示在首页</li>
        <li>请勿上传侵权、违法或低质量的资料</li>
        <li>资料审核通常在1-2个工作日内完成</li>
        <li>您可以在"我的资料"中查看资料审核状态</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import LoadingSpinner from '../components/LoadingSpinner.vue'
import { uploadResource } from '../api/resource'
import { useUserStore } from '../store/user'
import { getToken } from '../utils/auth'

const router = useRouter()
const userStore = useUserStore()

// Check login status on component mount
onMounted(() => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再上传资料')
    router.push('/login?redirect=/upload-resource')
    return
  }


})

// Upload action (not actually used since we handle upload manually)
const uploadAction = '#'

// Form data
const uploadForm = reactive({
  name: '',
  examType: null,
  description: ''
})

// File list
const fileList = ref([])

// Form validation rules
const uploadRules = {
  name: [
    { required: true, message: '请输入资料名称', trigger: 'blur' },
    { max: 50, message: '资料名称不能超过50个字符', trigger: 'blur' }
  ],
  examType: [
    { required: true, message: '请选择考试类型', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '资料简介不能超过200个字符', trigger: 'blur' }
  ],
  file: [
    { required: true, message: '请上传资料文件', trigger: 'change' }
  ]
}

// Form ref
const uploadFormRef = ref(null)

// Loading state
const uploading = ref(false)

// Handle file change
const handleFileChange = (file, files) => {
  // Check file size (30MB limit)
  const maxSize = 30 * 1024 * 1024 // 30MB in bytes
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过30MB')
    fileList.value = []
    return
  }

  // Update our reactive fileList
  fileList.value = [file]
}

// Handle file remove
const handleFileRemove = () => {
  fileList.value = []
}



// Handle upload
const handleSimpleUpload = async () => {
  // Basic validation
  if (!uploadForm.name) {
    ElMessage.error('请输入资料名称')
    return
  }

  if (uploadForm.examType === null) {
    ElMessage.error('请选择考试类型')
    return
  }

  if (fileList.value.length === 0) {
    ElMessage.error('请上传资料文件')
    return
  }

  uploading.value = true

  try {
    const formData = new FormData()
    formData.append('name', uploadForm.name)
    formData.append('examType', uploadForm.examType)

    if (uploadForm.description) {
      formData.append('description', uploadForm.description)
    }

    // Get file
    const file = fileList.value[0]
    const rawFile = file.raw || file
    formData.append('file', rawFile)

    const xhr = new XMLHttpRequest()
    xhr.open('POST', '/api/resources/upload', true)

    const token = getToken()
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }

    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          if (response.success) {
            ElMessage.success('资料上传成功，请等待管理员审核')
            router.push('/my-resources')
          } else {
            ElMessage.error(response.message || '上传失败，请稍后重试')
          }
        } catch (error) {
          ElMessage.error('响应解析失败，请稍后重试')
        }
      } else {
        ElMessage.error('上传失败，请稍后重试')
      }
      uploading.value = false
    }

    xhr.onerror = function() {
      ElMessage.error('网络错误，请稍后重试')
      uploading.value = false
    }

    xhr.send(formData)

  } catch (error) {
    ElMessage.error('上传失败，请稍后重试')
    uploading.value = false
  }
}
</script>

<style lang="scss" scoped>
.upload-resource {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .upload-form-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
  }

  .upload-file {
    width: 100%;
  }

  .upload-notice {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    h3 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 500;
      color: #303133;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }
}

@media (max-width: 768px) {
  .upload-resource {
    .upload-form-container {
      padding: 15px;
    }
  }
}
</style>
