import{W as A,u as ne,r as b,x as O,c as H,o as ie,b as R,d as u,e as a,f as o,g as y,Y as de,E as _,s as ue,l as V,q as me,m as c,t as h,h as J,j as L,G as M,Z as ce,_ as pe,D as fe}from"./index-CWlqViBh.js";import{f as ge}from"./ip-BIZqVrX-.js";import{_ as we}from"./_plugin-vue_export-helper-DlAUqK2U.js";function ve(k){return console.log("Send email code request:",{email:k}),new Promise((p,s)=>{const r=new XMLHttpRequest,t=`/api/users/email/send-code?email=${encodeURIComponent(k)}`;r.open("POST",t,!0);const m=A();m&&r.setRequestHeader("Authorization",`Bearer ${m}`),r.onload=function(){if(r.status>=200&&r.status<300)try{const l=JSON.parse(r.responseText);console.log("Send email code success response:",l),p(l)}catch(l){console.error("Error parsing response:",l),s(new Error("Invalid response format"))}else{console.error("Send email code failed with status:",r.status),console.error("Response text:",r.responseText);try{const l=JSON.parse(r.responseText);s(new Error(l.message||`Request failed: ${r.status} ${r.statusText}`))}catch{s(new Error(`Request failed: ${r.status} ${r.statusText}`))}}},r.onerror=function(){console.error("Network error during send email code"),s(new Error("Network error during send email code"))},r.send()})}function _e(k,p){return console.log("Bind email request:",{email:k,code:p}),new Promise((s,r)=>{const t=new XMLHttpRequest,m=`/api/users/email/bind?email=${encodeURIComponent(k)}&code=${encodeURIComponent(p)}`;t.open("POST",m,!0);const l=A();l&&t.setRequestHeader("Authorization",`Bearer ${l}`),t.onload=function(){if(t.status>=200&&t.status<300)try{const x=JSON.parse(t.responseText);console.log("Bind email success response:",x),s(x)}catch(x){console.error("Error parsing response:",x),r(new Error("Invalid response format"))}else{console.error("Bind email failed with status:",t.status),console.error("Response text:",t.responseText);try{const x=JSON.parse(t.responseText);r(new Error(x.message||`Request failed: ${t.status} ${t.statusText}`))}catch{r(new Error(`Request failed: ${t.status} ${t.statusText}`))}}},t.onerror=function(){console.error("Network error during bind email"),r(new Error("Network error during bind email"))},t.send()})}function be(){return console.log("Unbind email request"),new Promise((k,p)=>{const s=new XMLHttpRequest;s.open("DELETE","/api/users/email/unbind",!0);const t=A();t&&s.setRequestHeader("Authorization",`Bearer ${t}`),s.onload=function(){if(s.status>=200&&s.status<300)try{const m=JSON.parse(s.responseText);console.log("Unbind email success response:",m),k(m)}catch(m){console.error("Error parsing response:",m),p(new Error("Invalid response format"))}else{console.error("Unbind email failed with status:",s.status),console.error("Response text:",s.responseText);try{const m=JSON.parse(s.responseText);p(new Error(m.message||`Request failed: ${s.status} ${s.statusText}`))}catch{p(new Error(`Request failed: ${s.status} ${s.statusText}`))}}},s.onerror=function(){console.error("Network error during unbind email"),p(new Error("Network error during unbind email"))},s.send()})}const ye={class:"profile"},xe={class:"profile-info"},he={class:"avatar-preview"},ke={class:"email-hint"},Ve={key:0,style:{color:"#999"}},Pe={key:1,style:{color:"#67c23a"}},Re={style:{"margin-left":"10px"}},Ee={class:"change-password"},Ue={class:"email-binding"},Te={class:"card-header"},Ce={key:0,class:"bind-email"},$e={style:{display:"flex",gap:"10px"}},qe={key:1,class:"email-info"},Be={style:{"margin-bottom":"15px"}},Fe={class:"account-info"},Ne={__name:"Profile",setup(k){const p=ne(),s=b("info"),r=b(!1),t=b(!1),m=b(!1),l=b({}),x=b(null),B=b(null),n=O({username:"",nickname:"",avatar:"",email:"",phone:""}),f=O({oldPassword:"",newPassword:"",confirmPassword:""}),F=b(null),g=O({email:"",code:""}),E=b(!1),N=b(!1),C=b(!1),P=b(0);let S=null;const z=H(()=>{const d=n.nickname||l.value.username||"";return d?d.charAt(0).toUpperCase():"U"}),X=H(()=>l.value.role===1?"管理员":"普通用户"),Z={nickname:[{max:20,message:"昵称不能超过20个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},G={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:8,max:16,message:"密码长度必须在8-16个字符之间",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d).+$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(d,e,w)=>{e!==f.newPassword?w(new Error("两次输入的密码不一致")):w()},trigger:"blur"}]},W={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:6,message:"验证码为6位数字",trigger:"blur"}]},$=async()=>{r.value=!0;try{const d=await de();l.value=d.data,n.username=l.value.username,n.nickname=l.value.nickname||"",n.avatar=l.value.avatar||"",n.email=l.value.email||"",n.phone=l.value.phone||""}catch(d){console.error("Failed to fetch user info:",d),_.error("获取用户信息失败")}finally{r.value=!1}},Y=async()=>{x.value&&await x.value.validate(async d=>{if(d){t.value=!0;try{await ce({nickname:n.nickname,avatar:n.avatar,email:n.email,phone:n.phone}),_.success("个人资料更新成功"),await $(),await p.fetchCurrentUser()}catch(e){console.error("Failed to update profile:",e),_.error("更新个人资料失败")}finally{t.value=!1}}})},K=async()=>{B.value&&await B.value.validate(async d=>{if(d){m.value=!0;try{await pe(f.oldPassword,f.newPassword),_.success("密码修改成功，请重新登录"),f.oldPassword="",f.newPassword="",f.confirmPassword="",p.logout(),window.location.href="/login"}catch(e){console.error("Failed to change password:",e),_.error(e.message||"修改密码失败")}finally{m.value=!1}}})},Q=async()=>{if(!g.email){_.error("请先输入邮箱地址");return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g.email)){_.error("请输入正确的邮箱地址");return}E.value=!0;try{await ve(g.email),_.success("验证码已发送到您的邮箱"),C.value=!0,P.value=60,S=setInterval(()=>{P.value--,P.value<=0&&(clearInterval(S),S=null)},1e3)}catch(e){console.error("Failed to send verification code:",e),_.error(e.message||"发送验证码失败")}finally{E.value=!1}},j=async()=>{F.value&&await F.value.validate(async d=>{if(d){N.value=!0;try{await _e(g.email,g.code),_.success("邮箱绑定成功"),g.email="",g.code="",C.value=!1,await $(),await p.fetchCurrentUser()}catch(e){console.error("Failed to bind email:",e),_.error(e.message||"邮箱绑定失败")}finally{N.value=!1}}})},ee=()=>{fe.confirm("解绑邮箱后将无法使用邮箱找回密码，确定要解绑吗？","确认解绑",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await be(),_.success("邮箱解绑成功"),await $(),await p.fetchCurrentUser()}catch(d){console.error("Failed to unbind email:",d),_.error(d.message||"邮箱解绑失败")}}).catch(()=>{})};return ie(()=>{$()}),(d,e)=>{const w=y("el-input"),v=y("el-form-item"),ae=y("el-avatar"),oe=y("router-link"),U=y("el-button"),I=y("el-form"),q=y("el-tab-pane"),D=y("el-tag"),se=y("el-card"),T=y("el-descriptions-item"),le=y("el-descriptions"),re=y("el-tabs"),te=ue("loading");return V(),R("div",ye,[e[21]||(e[21]=u("h2",{class:"page-title"},"个人中心",-1)),a(re,{modelValue:s.value,"onUpdate:modelValue":e[11]||(e[11]=i=>s.value=i)},{default:o(()=>[a(q,{label:"个人资料",name:"info"},{default:o(()=>[me((V(),R("div",xe,[a(I,{ref_key:"profileFormRef",ref:x,model:n,rules:Z,"label-width":"100px"},{default:o(()=>[a(v,{label:"用户名"},{default:o(()=>[a(w,{modelValue:n.username,"onUpdate:modelValue":e[0]||(e[0]=i=>n.username=i),disabled:""},null,8,["modelValue"])]),_:1}),a(v,{label:"昵称",prop:"nickname"},{default:o(()=>[a(w,{modelValue:n.nickname,"onUpdate:modelValue":e[1]||(e[1]=i=>n.nickname=i),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),a(v,{label:"头像",prop:"avatar"},{default:o(()=>[a(w,{modelValue:n.avatar,"onUpdate:modelValue":e[2]||(e[2]=i=>n.avatar=i),placeholder:"请输入头像URL"},null,8,["modelValue"]),u("div",he,[a(ae,{size:64,src:n.avatar},{default:o(()=>[c(h(z.value),1)]),_:1},8,["src"])])]),_:1}),a(v,{label:"邮箱"},{default:o(()=>[a(w,{modelValue:n.email,"onUpdate:modelValue":e[3]||(e[3]=i=>n.email=i),disabled:""},null,8,["modelValue"]),u("div",ke,[n.email?(V(),R("span",Pe,"已绑定邮箱")):(V(),R("span",Ve,"未绑定邮箱")),u("span",Re,[a(oe,{to:"#",onClick:e[4]||(e[4]=i=>s.value="email"),style:{color:"#409eff"}},{default:o(()=>[c(h(n.email?"更换邮箱":"绑定邮箱"),1)]),_:1})])])]),_:1}),a(v,{label:"手机号",prop:"phone"},{default:o(()=>[a(w,{modelValue:n.phone,"onUpdate:modelValue":e[5]||(e[5]=i=>n.phone=i),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),a(v,null,{default:o(()=>[a(U,{type:"primary",loading:t.value,onClick:Y},{default:o(()=>e[12]||(e[12]=[c(" 保存修改 ")])),_:1,__:[12]},8,["loading"])]),_:1})]),_:1},8,["model"])])),[[te,r.value]])]),_:1}),a(q,{label:"修改密码",name:"password"},{default:o(()=>[u("div",Ee,[a(I,{ref_key:"passwordFormRef",ref:B,model:f,rules:G,"label-width":"100px"},{default:o(()=>[a(v,{label:"原密码",prop:"oldPassword"},{default:o(()=>[a(w,{modelValue:f.oldPassword,"onUpdate:modelValue":e[6]||(e[6]=i=>f.oldPassword=i),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),a(v,{label:"新密码",prop:"newPassword"},{default:o(()=>[a(w,{modelValue:f.newPassword,"onUpdate:modelValue":e[7]||(e[7]=i=>f.newPassword=i),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(v,{label:"确认密码",prop:"confirmPassword"},{default:o(()=>[a(w,{modelValue:f.confirmPassword,"onUpdate:modelValue":e[8]||(e[8]=i=>f.confirmPassword=i),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1}),a(v,null,{default:o(()=>[a(U,{type:"primary",loading:m.value,onClick:K},{default:o(()=>e[13]||(e[13]=[c(" 修改密码 ")])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1},8,["model"])])]),_:1}),a(q,{label:"邮箱绑定",name:"email"},{default:o(()=>[u("div",Ue,[a(se,{class:"email-binding-card"},{header:o(()=>[u("div",Te,[e[16]||(e[16]=u("span",null,"邮箱绑定",-1)),l.value.email?(V(),J(D,{key:1,type:"success"},{default:o(()=>e[15]||(e[15]=[c("已绑定")])),_:1,__:[15]})):(V(),J(D,{key:0,type:"warning"},{default:o(()=>e[14]||(e[14]=[c("未绑定")])),_:1,__:[14]}))])]),default:o(()=>[l.value.email?(V(),R("div",qe,[u("p",Be,[e[19]||(e[19]=u("strong",null,"已绑定邮箱：",-1)),c(h(l.value.email),1)]),a(U,{onClick:ee},{default:o(()=>e[20]||(e[20]=[c("解绑邮箱")])),_:1,__:[20]})])):(V(),R("div",Ce,[e[18]||(e[18]=u("div",{class:"benefits"},[u("p",{style:{"margin-bottom":"15px","font-weight":"500"}},"绑定邮箱后可以："),u("ul",{style:{margin:"0","padding-left":"20px",color:"#666"}},[u("li",null,"🔐 使用邮箱找回密码"),u("li",null,"📧 接收重要通知"),u("li",null,"🛡️ 提升账号安全性")])],-1)),a(I,{ref_key:"emailBindFormRef",ref:F,model:g,rules:W,"label-width":"80px",style:{"margin-top":"20px"}},{default:o(()=>[a(v,{label:"邮箱",prop:"email"},{default:o(()=>[a(w,{modelValue:g.email,"onUpdate:modelValue":e[9]||(e[9]=i=>g.email=i),placeholder:"请输入邮箱地址",disabled:E.value},null,8,["modelValue","disabled"])]),_:1}),a(v,{label:"验证码",prop:"code"},{default:o(()=>[u("div",$e,[a(w,{modelValue:g.code,"onUpdate:modelValue":e[10]||(e[10]=i=>g.code=i),placeholder:"请输入验证码",style:{flex:"1"},disabled:!C.value},null,8,["modelValue","disabled"]),a(U,{disabled:!g.email||E.value||P.value>0,loading:E.value,onClick:Q},{default:o(()=>[c(h(P.value>0?`${P.value}s后重发`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),a(v,null,{default:o(()=>[a(U,{type:"primary",loading:N.value,disabled:!C.value,onClick:j},{default:o(()=>e[17]||(e[17]=[c(" 绑定邮箱 ")])),_:1,__:[17]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]))]),_:1})])]),_:1}),a(q,{label:"账号信息",name:"account"},{default:o(()=>[u("div",Fe,[a(le,{title:"账号信息",column:1,border:""},{default:o(()=>[a(T,{label:"用户名"},{default:o(()=>[c(h(l.value.username),1)]),_:1}),a(T,{label:"用户角色"},{default:o(()=>[c(h(X.value),1)]),_:1}),a(T,{label:"注册时间"},{default:o(()=>[c(h(L(M)(l.value.createTime)),1)]),_:1}),a(T,{label:"最后登录时间"},{default:o(()=>[c(h(L(M)(l.value.lastLoginTime)),1)]),_:1}),a(T,{label:"最后登录IP"},{default:o(()=>[c(h(L(ge)(l.value.lastLoginIp)),1)]),_:1})]),_:1})])]),_:1})]),_:1},8,["modelValue"])])}}},Ae=we(Ne,[["__scopeId","data-v-df1b9bcc"]]);export{Ae as default};
