import { ElMessage, ElNotification } from 'element-plus'
import router from '../router'

/**
 * 全局错误处理器
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 选项
   */
  static handleApiError(error, options = {}) {
    const { showMessage = true, showNotification = false } = options
    
    console.error('API Error:', error)
    
    // 网络错误
    if (!error.response) {
      if (showMessage) {
        ElMessage.error('网络连接异常，请检查网络设置')
      }
      if (showNotification) {
        ElNotification.error({
          title: '网络错误',
          message: '无法连接到服务器，请稍后重试'
        })
      }
      // 跳转到网络错误页面
      router.push('/network-error')
      return
    }
    
    const status = error.response.status
    const data = error.response.data
    
    switch (status) {
      case 400:
        this.handleBadRequest(data, showMessage)
        break
      case 401:
        this.handleUnauthorized(data, showMessage)
        break
      case 403:
        this.handleForbidden(data, showMessage)
        break
      case 404:
        this.handleNotFound(data, showMessage)
        break
      case 500:
        this.handleServerError(data, showMessage)
        break
      default:
        this.handleGenericError(error, showMessage)
    }
  }
  
  /**
   * 处理400错误
   */
  static handleBadRequest(data, showMessage) {
    const message = data?.message || '请求参数错误'
    if (showMessage) {
      ElMessage.error(message)
    }
  }
  
  /**
   * 处理401错误
   */
  static handleUnauthorized(data, showMessage) {
    const message = data?.message || '登录已过期，请重新登录'
    if (showMessage) {
      ElMessage.error(message)
    }
    
    // 清除本地存储的用户信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // 跳转到登录页面
    router.push('/login')
  }
  
  /**
   * 处理403错误
   */
  static handleForbidden(data, showMessage) {
    const message = data?.message || '没有权限访问该资源'
    if (showMessage) {
      ElMessage.error(message)
    }
    
    // 跳转到首页
    router.push('/')
  }
  
  /**
   * 处理404错误
   */
  static handleNotFound(data, showMessage) {
    const message = data?.message || '请求的资源不存在'
    if (showMessage) {
      ElMessage.error(message)
    }
    
    // 跳转到404页面
    router.push('/404')
  }
  
  /**
   * 处理500错误
   */
  static handleServerError(data, showMessage) {
    const message = data?.message || '服务器内部错误'
    if (showMessage) {
      ElMessage.error(message)
    }
    
    // 跳转到500错误页面
    router.push({
      path: '/500',
      query: { error: message }
    })
  }
  
  /**
   * 处理通用错误
   */
  static handleGenericError(error, showMessage) {
    const message = error.message || '发生未知错误'
    if (showMessage) {
      ElMessage.error(message)
    }
    
    console.error('Generic Error:', error)
  }
  
  /**
   * 处理文件上传错误
   */
  static handleUploadError(error) {
    console.error('Upload Error:', error)
    
    if (error.code === 'NETWORK_ERROR') {
      ElMessage.error('网络连接异常，上传失败')
      return
    }
    
    if (error.code === 'FILE_TOO_LARGE') {
      ElMessage.error('文件大小超出限制')
      return
    }
    
    if (error.code === 'INVALID_FILE_TYPE') {
      ElMessage.error('不支持的文件类型')
      return
    }
    
    ElMessage.error(error.message || '文件上传失败')
  }
  
  /**
   * 处理Vue应用错误
   */
  static handleVueError(error, instance, info) {
    console.error('Vue Error:', error)
    console.error('Component:', instance)
    console.error('Info:', info)
    
    ElNotification.error({
      title: '应用错误',
      message: '页面出现异常，请刷新重试',
      duration: 5000
    })
  }
  
  /**
   * 处理Promise未捕获的错误
   */
  static handleUnhandledRejection(event) {
    console.error('Unhandled Promise Rejection:', event.reason)
    
    ElNotification.warning({
      title: '操作异常',
      message: '某个操作未能正常完成，请重试',
      duration: 3000
    })
    
    // 阻止默认的错误处理
    event.preventDefault()
  }
  
  /**
   * 处理JavaScript运行时错误
   */
  static handleJavaScriptError(event) {
    console.error('JavaScript Error:', event.error)
    
    ElNotification.error({
      title: '脚本错误',
      message: '页面脚本执行异常，建议刷新页面',
      duration: 5000
    })
  }
}

/**
 * 安装全局错误处理器
 */
export function setupErrorHandler(app) {
  // Vue应用错误处理
  app.config.errorHandler = ErrorHandler.handleVueError
  
  // 全局未捕获的Promise错误
  window.addEventListener('unhandledrejection', ErrorHandler.handleUnhandledRejection)
  
  // 全局JavaScript错误
  window.addEventListener('error', ErrorHandler.handleJavaScriptError)
}
