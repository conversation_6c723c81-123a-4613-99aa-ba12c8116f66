# 生产环境变量配置文件
# 根据已知信息创建的完整配置

# ================================
# 基础环境配置
# ================================
SPRING_PROFILES_ACTIVE=prod

# ================================
# 数据库配置
# ================================
DB_URL=************************************************************************************************************
DB_USERNAME=gongxingxue
DB_PASSWORD=gxxdb123456

# 数据库连接池配置
DB_MIN_IDLE=20
DB_MAX_POOL_SIZE=50

# ================================
# 服务器配置
# ================================
SERVER_PORT=8081

# ================================
# 邮件配置
# ================================
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=xzkzrwqzuditdhch

# ================================
# JWT配置
# ================================
JWT_SECRET=gongxingxue_prod_secret_key_2024_baota_panel_deployment
JWT_EXPIRATION=86400000

# ================================
# CORS和前端配置
# ================================
CORS_ALLOWED_ORIGINS=http://************,http://localhost:3000
FRONTEND_URL=http://************

# ================================
# 文件存储配置
# ================================
FILE_UPLOAD_DIR=/www/wwwroot/gongxingxue/uploads

# ================================
# 日志配置
# ================================
LOG_FILE=/www/wwwroot/gongxingxue/logs/application.log

# ================================
# 联系信息配置
# ================================
CONTACT_EMAIL=<EMAIL>

# ================================
# 使用说明
# ================================
# 1. 将此文件重命名为 .env 或根据您的项目需要命名
# 2. 确保文件权限设置正确（建议 600）
# 3. 在应用启动前加载这些环境变量
# 4. 生产环境中请确保此文件的安全性

# ================================
# 部署信息
# ================================
# 服务器IP: ************
# 前端访问: http://************
# 后端API: http://************:8081/api
# 管理员账户: admin/admin123
# 数据库: gongxingxue (用户: gongxingxue, 密码: gxxdb123456)
